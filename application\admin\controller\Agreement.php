<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use think\Db;
use think\Exception;

/**
 * 协议配置
 *
 * @icon fa fa-circle-o
 */
class Agreement extends Backend
{

    /**
     * Agreement模型对象
     * @var \app\admin\model\Agreement
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Agreement;
    }

    /**
     * 查看
     */
    public function index()
    {
        if ($this->request->isAjax()) {
            // 如果是Ajax请求，返回原来的表格数据格式
            return parent::index();
        }

        // 获取所有协议配置
        $agreementList = $this->model->select();
        $this->view->assign('agreementList', $agreementList);
        return $this->view->fetch();
    }

    /**
     * 编辑协议配置
     */
    public function edit($ids = null)
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                Db::startTrans();
                try {
                    foreach ($params as $id => $content) {
                        $this->model->where('id', $id)->update(['content' => $content]);
                    }
                    Db::commit();
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                $this->success('保存成功');
            }
            $this->error('参数错误');
        }
        $this->error('非法请求');
    }

    /**
     * 禁用添加功能
     */
    public function add()
    {
        $this->error('协议配置不支持添加操作');
    }

    /**
     * 禁用删除功能
     */
    public function del($ids = null)
    {
        $this->error('协议配置不支持删除操作');
    }

    /**
     * 禁用批量操作
     */
    public function multi($ids = null)
    {
        $this->error('协议配置不支持批量操作');
    }
}
