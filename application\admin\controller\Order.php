<?php

namespace app\admin\controller;

use app\common\controller\Backend;

/**
 * 用户付费记录
 *
 * @icon fa fa-circle-o
 */
class Order extends Backend
{

    /**
     * Order模型对象
     * @var \app\admin\model\Order
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Order;
        $this->view->assign("statusList", $this->model->getStatusList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams(null,true);

            $list = $this->model
                    ->with(['user', 'companyCategory'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                // 用户信息
                if ($row->user) {
                    $row->getRelation('user')->visible(['id', 'nickname', 'avatar']);
                }

                // 企业分类信息
                if ($row->companyCategory) {
                    $row['company_category_name'] = $row->companyCategory->name;

                    // 根据分类的code字段和link_id获取关联数据的name
                    $row['link_name'] = $this->getLinkName($row->companyCategory->code, $row->link_id);
                } else {
                    $row['company_category_name'] = '';
                    $row['link_name'] = '';
                }

                // 格式化金额
                $row['amount'] = number_format($row->amount, 2);

                // 格式化时间
                $row['createtime_text'] = date('Y-m-d H:i:s', $row->createtime);
                $row['paytime_text'] = $row->paytime ? date('Y-m-d H:i:s', $row->paytime) : '';

                // 状态文本
                $row['status_text'] = $this->getStatusText($row->status);
                $row['paytype_text'] = $this->getPaytypeText($row->paytype);

                // 清理关联数据，避免输出过多数据
                unset($row->companyCategory);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 获取状态文本
     */
    private function getStatusText($status)
    {
        $statusMap = [
            'pending' => '待支付',
            'paid' => '已支付'
        ];
        return $statusMap[$status] ?? '未知状态';
    }

    /**
     * 获取支付方式文本
     */
    private function getPaytypeText($paytype)
    {
        if (!$paytype) {
            return '';
        }
        $paytypeMap = [
            'wechat' => '微信支付',
            'alipay' => '支付宝'
        ];
        return $paytypeMap[$paytype] ?? $paytype;
    }

    /**
     * 根据分类code和link_id获取关联数据的name
     */
    private function getLinkName($code, $linkId)
    {
        if (!$code || !$linkId) {
            return '';
        }

        try {
            // 根据code确定表名，查询对应记录的name字段
            $result = \think\Db::name($code)->where('id', $linkId)->value('name');
            return $result ?: '';
        } catch (\Exception $e) {
            // 如果表不存在或查询失败，返回空字符串
            return '';
        }
    }

}
