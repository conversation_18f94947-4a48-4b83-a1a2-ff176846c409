<?php

namespace app\common\model;

use think\Model;

/**
 * 企业审核模型
 */
class Company extends Model
{
    // 表名
    protected $name = 'company';
    
    // 开启自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';
    
    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false; // 不使用updatetime字段
    
    // 追加属性
    protected $append = [
        'status_text'
    ];

    /**
     * 获取状态列表
     */
    public function getStatusList()
    {
        return [
            'pending' => '审核中',
            'pass' => '已通过', 
            'refuse' => '已拒绝'
        ];
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : $data['status'];
        $list = $this->getStatusList();
        return $list[$value] ?? '';
    }

    /**
     * 关联用户模型
     */
    public function user()
    {
        return $this->belongsTo('User', 'user_id');
    }

    /**
     * 关联分类模型
     */
    public function category()
    {
        return $this->belongsTo('Category', 'company_category_id');
    }
}
