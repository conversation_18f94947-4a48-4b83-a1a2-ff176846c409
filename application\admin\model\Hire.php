<?php

namespace app\admin\model;

use think\Model;


class Hire extends Model
{

    

    

    // 表名
    protected $name = 'hire';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'kind_text'
    ];
    

    
    public function getKindList()
    {
        return ['driver' => __('Kind driver'), 'repair' => __('Kind repair')];
    }


    public function getKindTextAttr($value, $data)
    {
        $value = $value ?: ($data['kind'] ?? '');
        $list = $this->getKindList();
        return $list[$value] ?? '';
    }

    public function user()
    {
        return $this->belongsTo('User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    public function hireCar()
    {
        return $this->belongsTo('HireCar', 'hire_car_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    public function hireLicense()
    {
        return $this->belongsTo('HireLicense', 'hire_license_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    public function hireExperience()
    {
        return $this->belongsTo('HireExperience', 'hire_experience_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

}
