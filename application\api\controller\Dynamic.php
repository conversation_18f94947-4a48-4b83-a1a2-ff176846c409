<?php

namespace app\api\controller;

use app\common\controller\Api;
use think\Db;

/**
 * 卡友江湖接口
 */
class Dynamic extends Api
{
    protected $noNeedLogin = ['helpList', 'circleList', 'dynamicList', 'detail'];
    protected $noNeedRight = ['*'];

    /**
     * 求助列表
     *
     * @ApiMethod (GET)
     * @ApiParams (name="page", type="int", required=false, description="页码，默认1")
     * @ApiParams (name="limit", type="int", required=false, description="每页数量，默认10")
     * @ApiParams (name="category_id", type="int", required=false, description="求助类型ID")
     */
    public function helpList()
    {
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);
        $category_id = $this->request->get('category_id', 0);

        $where = ['kind' => 'help', 'status' => 'pass'];
        if ($category_id) {
            $where['jianghu_category_id'] = $category_id;
        }

        $list = Db::name('jianghu')
            ->where($where)
            ->order('createtime desc')
            ->page($page, $limit)
            ->select();
        if ($this->auth->isLogin()) {
            $user = $this->auth->getUser();
        } else {
            $user = null;
        }
        // 获取每个帖子的点赞数和评论数
        foreach ($list as &$item) {
            $item['click_count'] = Db::name('jianghu_click')->where('jianghu_id', $item['id'])->count();
            $item['comment_count'] = Db::name('jianghu_comment')->where('jianghu_id', $item['id'])->count();
            $item['images'] = $item['images'] ? explode(',', $item['images']) : [];
            $item['createtime_text'] = datetime($item['createtime']);
        }

        $this->success('获取成功', $list);
    }

    /**
     * 圈子列表
     *
     * @ApiMethod (GET)
     * @ApiParams (name="page", type="int", required=false, description="页码，默认1")
     * @ApiParams (name="limit", type="int", required=false, description="每页数量，默认10")
     */
    public function circleList()
    {
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);

        $list = Db::name('jianghu')
            ->where(['kind' => 'circle', 'status' => 'pass'])
            ->order('createtime desc')
            ->page($page, $limit)
            ->select();
        if ($this->auth->isLogin()) {
            $user = $this->auth->getUser();
        } else {
            $user = null;
        }
        // 获取每个帖子的点赞数和评论数
        foreach ($list as &$item) {
            $item['click_count'] = Db::name('jianghu_click')->where('jianghu_id', $item['id'])->count();
            $item['comment_count'] = Db::name('jianghu_comment')->where('jianghu_id', $item['id'])->count();
            $item['images'] = $item['images'] ? explode(',', $item['images']) : [];
            // 如果用户已登录，检查是否已点赞
            $detail['is_clicked'] = $user ? ($item['is_clicked'] = Db::name('jianghu_click')
                ->where(['jianghu_id' => $item['id'], 'user_id' => $user->id])
                ->count() > 0 ? true : false) : false;
            $item['createtime_text'] = datetime($item['createtime']);
        }

        $this->success('获取成功', $list);
    }

    /**
     * 动态列表
     *
     * @ApiMethod (GET)
     * @ApiParams (name="page", type="int", required=false, description="页码，默认1")
     * @ApiParams (name="limit", type="int", required=false, description="每页数量，默认10")
     */
    public function dynamicList()
    {
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);

        $list = Db::name('jianghu')
            ->where(['kind' => 'dynamic', 'status' => 'pass'])
            ->order('createtime desc')
            ->page($page, $limit)
            ->select();
        if ($this->auth->isLogin()) {
            $user = $this->auth->getUser();
        } else {
            $user = null;
        }
        // 获取每个帖子的点赞数和评论数
        foreach ($list as &$item) {
            $item['click_count'] = Db::name('jianghu_click')->where('jianghu_id', $item['id'])->count();
            $item['comment_count'] = Db::name('jianghu_comment')->where('jianghu_id', $item['id'])->count();
            $item['images'] = $item['images'] ? explode(',', $item['images']) : [];
            // 如果用户已登录，检查是否已点赞
            $detail['is_clicked'] = $user ? ($item['is_clicked'] = Db::name('jianghu_click')
                ->where(['jianghu_id' => $item['id'], 'user_id' => $user->id])
                ->count() > 0 ? true : false) : false;
            $item['createtime_text'] = datetime($item['createtime']);
        }

        $this->success('获取成功', $list);
    }

    /** 
     * 帖子详情
     *
     * @ApiMethod (GET)
     * @ApiParams (name="id", type="int", required=true, description="帖子ID")
     */
    public function detail()
    {
        $id = $this->request->get('id');
        if (!$id) {
            $this->error(__('Invalid parameters'));
        }

        $detail = Db::name('jianghu')->where('id', $id)->find();
        if (!$detail) {
            $this->error('帖子不存在');
        }

        // 增加浏览量
        Db::name('jianghu')->where('id', $id)->setInc('show');

        // 获取点赞数和评论数
        $detail['click_count'] = Db::name('jianghu_click')->where('jianghu_id', $id)->count();
        $detail['comment_count'] = Db::name('jianghu_comment')->where('jianghu_id', $id)->count();
        $detail['images'] = $detail['images'] ? explode(',', $detail['images']) : [];
        $detail['createtime_text'] = datetime($detail['createtime']);

        // 获取评论列表
        $comments = Db::name('jianghu_comment')
            ->where('jianghu_id', $id)
            ->order('createtime desc')
            ->select();
        foreach ($comments as &$comment) {
            $comment['createtime_text'] = datetime($comment['createtime']);
        }
        $detail['comments'] = $comments;

        // 如果用户已登录，检查是否已点赞
        if ($this->auth->isLogin()) {
            $user = $this->auth->getUser();
            $detail['is_clicked'] = Db::name('jianghu_click')
                ->where(['jianghu_id' => $id, 'user_id' => $user->id])
                ->count() > 0;
        } else {
            $detail['is_clicked'] = false;
        }

        $this->success('获取成功', $detail);
    }

    /**
     * 点赞/取消点赞
     *
     * @ApiMethod (POST)
     * @ApiParams (name="jianghu_id", type="int", required=true, description="帖子ID")
     */
    public function toggleClick()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $jianghu_id = $this->request->post('jianghu_id');
        if (!$jianghu_id) {
            $this->error(__('Invalid parameters'));
        }

        // 检查帖子是否存在
        $jianghu = Db::name('jianghu')->where('id', $jianghu_id)->find();
        if (!$jianghu) {
            $this->error('帖子不存在');
        }

        // 检查是否已点赞
        $existClick = Db::name('jianghu_click')
            ->where(['jianghu_id' => $jianghu_id, 'user_id' => $user->id])
            ->find();

        try {
            if ($existClick) {
                // 取消点赞
                Db::name('jianghu_click')->where('id', $existClick['id'])->delete();
                // 删除通知
                Db::name('jianghu_event')
                    ->where(['jianghu_id' => $jianghu_id, 'p_user_id' => $user->id, 'event' => 'click'])
                    ->delete();
                $action = '取消点赞';
                $is_clicked = false;
            } else {
                // 添加点赞
                Db::name('jianghu_click')->insert([
                    'user_id' => $user->id,
                    'jianghu_id' => $jianghu_id,
                    'createtime' => time()
                ]);

                // 如果不是自己的帖子，添加通知
                if ($jianghu['user_id'] != $user->id) {
                    Db::name('jianghu_event')->insert([
                        'user_id' => $jianghu['user_id'],
                        'kind' => $jianghu['kind'],
                        'jianghu_id' => $jianghu_id,
                        'p_user_id' => $user->id,
                        'nickname' => $user->nickname,
                        'avatar' => $user->avatar,
                        'event' => 'click',
                        'read' => 0,
                        'createtime' => time()
                    ]);
                }
                $action = '点赞';
                $is_clicked = true;
            }
        } catch (\Exception $e) {
            $this->error('操作失败，请重试');
        }

        // 获取最新点赞数
        $click_count = Db::name('jianghu_click')->where('jianghu_id', $jianghu_id)->count();

        $this->success($action . '成功', [
            'is_clicked' => $is_clicked,
            'click_count' => $click_count
        ]);
    }

    /**
     * 发表评论
     *
     * @ApiMethod (POST)
     * @ApiParams (name="jianghu_id", type="int", required=true, description="帖子ID")
     * @ApiParams (name="content", type="string", required=true, description="评论内容")
     */
    public function addComment()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $jianghu_id = $this->request->post('jianghu_id');
        $content = $this->request->post('content');

        if (!$jianghu_id || !$content) {
            $this->error(__('Invalid parameters'));
        }

        // 检查帖子是否存在
        $jianghu = Db::name('jianghu')->where('id', $jianghu_id)->find();
        if (!$jianghu) {
            $this->error('帖子不存在');
        }

        try {
            // 添加评论
            $comment_id = Db::name('jianghu_comment')->insertGetId([
                'jianghu_id' => $jianghu_id,
                'user_id' => $user->id,
                'nickname' => $user->nickname,
                'avatar' => $user->avatar,
                'content' => $content,
                'createtime' => time()
            ]);

            // 如果不是自己的帖子，添加通知
            if ($jianghu['user_id'] != $user->id) {
                Db::name('jianghu_event')->insert([
                    'user_id' => $jianghu['user_id'],
                    'kind' => $jianghu['kind'],
                    'jianghu_id' => $jianghu_id,
                    'p_user_id' => $user->id,
                    'nickname' => $user->nickname,
                    'avatar' => $user->avatar,
                    'event' => 'comment',
                    'read' => 0,
                    'createtime' => time()
                ]);
            }
        } catch (\Exception $e) {
            $this->error('评论失败，请重试');
        }

        // 获取最新评论数
        $comment_count = Db::name('jianghu_comment')->where('jianghu_id', $jianghu_id)->count();

        $this->success('评论成功', [
            'comment_id' => $comment_id,
            'comment_count' => $comment_count
        ]);
    }

    /**
     * 发布帖子
     *
     * @ApiMethod (POST)
     * @ApiParams (name="kind", type="string", required=true, description="类型：help=求助,circle=圈子,dynamic=动态")
     * @ApiParams (name="jianghu_category_id", type="int", required=false, description="求助类型ID（仅求助类型需要）")
     * @ApiParams (name="content", type="string", required=true, description="内容")
     * @ApiParams (name="images", type="string", required=false, description="图片/视频，多个用逗号分隔")
     * @ApiParams (name="address", type="string", required=false, description="地址")
     * @ApiParams (name="lng", type="string", required=false, description="经度")
     * @ApiParams (name="lat", type="string", required=false, description="纬度")
     */
    public function add()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $kind = $this->request->post('kind');
        $jianghu_category_id = $this->request->post('jianghu_category_id', 0);
        $content = $this->request->post('content');
        $images = $this->request->post('images', '');
        $address = $this->request->post('address', '');
        $lng = $this->request->post('lng', '');
        $lat = $this->request->post('lat', '');

        if (!$kind || !$content) {
            $this->error(__('Invalid parameters'));
        }

        if (!in_array($kind, ['help', 'circle', 'dynamic'])) {
            $this->error('类型参数错误');
        }

        // 如果是求助类型，必须选择求助分类
        if ($kind == 'help' && !$jianghu_category_id) {
            $this->error('请选择求助类型');
        }

        try {
            $data = [
                'kind' => $kind,
                'jianghu_category_id' => $jianghu_category_id ?: null,
                'user_id' => $user->id,
                'nickname' => $user->nickname,
                'avatar' => $user->avatar,
                'content' => $content,
                'images' => $images,
                'address' => $address,
                'lng' => $lng,
                'lat' => $lat,
                'createtime' => time(),
                'show' => 0,
                'status' => 'pending'
            ];

            $id = Db::name('jianghu')->insertGetId($data);
        } catch (\Exception $e) {
            $this->error('发布失败，请重试');
        }

        $this->success('发布成功，等待审核', ['id' => $id]);
    }

    /**
     * 我的帖子列表
     *
     * @ApiMethod (GET)
     * @ApiParams (name="page", type="int", required=false, description="页码，默认1")
     * @ApiParams (name="limit", type="int", required=false, description="每页数量，默认10")
     * @ApiParams (name="kind", type="string", required=false, description="类型筛选")
     */
    public function myList()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);
        $kind = $this->request->get('kind', '');

        $where = ['user_id' => $user->id];
        if ($kind) {
            $where['kind'] = $kind;
        }

        $list = Db::name('jianghu')
            ->where($where)
            ->order('createtime desc')
            ->page($page, $limit)
            ->select();

        // 获取每个帖子的点赞数和评论数，以及审核状态信息
        $statusText = [
            'pending' => '审核中',
            'pass' => '审核通过',
            'refuse' => '审核不通过'
        ];

        foreach ($list as &$item) {
            $item['click_count'] = Db::name('jianghu_click')->where('jianghu_id', $item['id'])->count();
            $item['comment_count'] = Db::name('jianghu_comment')->where('jianghu_id', $item['id'])->count();
            $item['images'] = $item['images'] ? explode(',', $item['images']) : [];
            $item['status_text'] = $statusText[$item['status']] ?? '未知状态';
        }

        $this->success('获取成功', $list);
    }

    /**
     * 删除帖子
     *
     * @ApiMethod (POST)
     * @ApiParams (name="id", type="int", required=true, description="帖子ID")
     */
    public function delete()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $id = $this->request->post('id');
        if (!$id) {
            $this->error(__('Invalid parameters'));
        }

        // 检查帖子是否存在且属于当前用户
        $jianghu = Db::name('jianghu')->where(['id' => $id, 'user_id' => $user->id])->find();
        if (!$jianghu) {
            $this->error('帖子不存在或无权限删除');
        }

        try {
            // 删除帖子
            Db::name('jianghu')->where('id', $id)->delete();
            // 删除相关点赞
            Db::name('jianghu_click')->where('jianghu_id', $id)->delete();
            // 删除相关评论
            Db::name('jianghu_comment')->where('jianghu_id', $id)->delete();
            // 删除相关通知
            Db::name('jianghu_event')->where('jianghu_id', $id)->delete();
        } catch (\Exception $e) {
            $this->error('删除失败，请重试');
        }

        $this->success('删除成功');
    }

    /**
     * 编辑帖子
     *
     * @ApiMethod (POST)
     * @ApiParams (name="id", type="int", required=true, description="帖子ID")
     * @ApiParams (name="kind", type="string", required=true, description="类型：help=求助,circle=圈子,dynamic=动态")
     * @ApiParams (name="jianghu_category_id", type="int", required=false, description="求助类型ID（仅求助类型需要）")
     * @ApiParams (name="content", type="string", required=true, description="内容")
     * @ApiParams (name="images", type="string", required=false, description="图片/视频，多个用逗号分隔")
     * @ApiParams (name="address", type="string", required=false, description="地址")
     * @ApiParams (name="lng", type="string", required=false, description="经度")
     * @ApiParams (name="lat", type="string", required=false, description="纬度")
     */
    public function edit()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $id = $this->request->post('id');
        $kind = $this->request->post('kind');
        $jianghu_category_id = $this->request->post('jianghu_category_id', 0);
        $content = $this->request->post('content');
        $images = $this->request->post('images', '');
        $address = $this->request->post('address', '');
        $lng = $this->request->post('lng', '');
        $lat = $this->request->post('lat', '');

        if (!$id || !$kind || !$content) {
            $this->error(__('Invalid parameters'));
        }

        if (!in_array($kind, ['help', 'circle', 'dynamic'])) {
            $this->error('类型参数错误');
        }

        // 检查帖子是否存在且属于当前用户
        $jianghu = Db::name('jianghu')->where(['id' => $id, 'user_id' => $user->id])->find();
        if (!$jianghu) {
            $this->error('帖子不存在或无权限编辑');
        }

        // 如果是求助类型，必须选择求助分类
        if ($kind == 'help' && !$jianghu_category_id) {
            $this->error('请选择求助类型');
        }

        try {
            $data = [
                'kind' => $kind,
                'jianghu_category_id' => $jianghu_category_id ?: null,
                'content' => $content,
                'images' => $images,
                'address' => $address,
                'lng' => $lng,
                'lat' => $lat,
                'status' => 'pending', // 重新提交审核
                'refuse_remark' => null // 清空拒绝原因
            ];

            Db::name('jianghu')->where('id', $id)->update($data);
        } catch (\Exception $e) {
            $this->error('编辑失败，请重试');
        }

        $this->success('编辑成功，等待审核');
    }

    /**
     * 获取帖子审核状态
     *
     * @ApiMethod (GET)
     * @ApiParams (name="id", type="int", required=true, description="帖子ID")
     */
    public function getAuditStatus()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $id = $this->request->get('id');
        if (!$id) {
            $this->error(__('Invalid parameters'));
        }

        // 检查帖子是否存在且属于当前用户
        $jianghu = Db::name('jianghu')->where(['id' => $id, 'user_id' => $user->id])->find();
        if (!$jianghu) {
            $this->error('帖子不存在或无权限查看');
        }

        $statusText = [
            'pending' => '审核中',
            'pass' => '审核通过',
            'refuse' => '审核不通过'
        ];

        $result = [
            'id' => $jianghu['id'],
            'status' => $jianghu['status'],
            'status_text' => $statusText[$jianghu['status']] ?? '未知状态',
            'refuse_remark' => $jianghu['refuse_remark'] ?? '',
            'createtime' => $jianghu['createtime']
        ];

        $this->success('获取成功', $result);
    }

    /**
     * 获取通知列表
     *
     * @ApiMethod (GET)
     * @ApiParams (name="page", type="int", required=false, description="页码，默认1")
     * @ApiParams (name="limit", type="int", required=false, description="每页数量，默认10")
     */
    public function eventList()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);

        $list = Db::name('jianghu_event')
            ->where('user_id', $user->id)
            ->order('createtime desc')
            ->page($page, $limit)
            ->select();

        // 获取帖子信息
        foreach ($list as &$item) {
            $jianghu = Db::name('jianghu')->where('id', $item['jianghu_id'])->find();
            $item['jianghu'] = $jianghu;
        }

        $this->success('获取成功', $list);
    }

    /**
     * 标记通知为已读
     *
     * @ApiMethod (POST)
     * @ApiParams (name="id", type="int", required=true, description="通知ID")
     */
    public function readEvent()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $id = $this->request->post('id');
        if (!$id) {
            $this->error(__('Invalid parameters'));
        }

        try {
            Db::name('jianghu_event')
                ->where(['id' => $id, 'user_id' => $user->id])
                ->update(['read' => 1]);
        } catch (\Exception $e) {
            $this->error('操作失败，请重试');
        }

        $this->success('操作成功');
    }

    /**
     * 获取未读通知数量
     *
     * @ApiMethod (GET)
     */
    public function unreadCount()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $count = Db::name('jianghu_event')
            ->where(['user_id' => $user->id, 'read' => 0])
            ->count();

        $this->success('获取成功', ['count' => $count]);
    }

    /**
     * 分享帖子
     *
     * @ApiMethod (POST)
     * @ApiParams (name="jianghu_id", type="int", required=true, description="帖子ID")
     */
    public function sharePost()
    {
        $jianghu_id = $this->request->post('jianghu_id');
        if (!$jianghu_id) {
            $this->error(__('Invalid parameters'));
        }

        // 检查帖子是否存在
        $post = Db::name('jianghu')->where('id', $jianghu_id)->find();
        if (!$post) {
            $this->error('帖子不存在');
        }

        // 增加分享数
        try {
            Db::name('jianghu')->where('id', $jianghu_id)->setInc('share', 1);
        } catch (\Exception $e) {
            $this->error('分享失败，请重试');
        }

        // 获取当前域名
        $domain = $this->request->domain();

        // 生成分享链接
        $shareUrl = $domain . '/h5/index.html?id=' . $jianghu_id;

        // 返回分享链接和更新后的分享数
        $newShareCount = Db::name('jianghu')->where('id', $jianghu_id)->value('share');

        $this->success('分享成功', [
            'share_url' => $shareUrl,
            'share_count' => $newShareCount,
            'jianghu_id' => $jianghu_id
        ]);
    }
}
