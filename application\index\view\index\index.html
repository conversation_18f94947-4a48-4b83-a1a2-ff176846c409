<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>快乐货运 - 专业·高效·放心</title>
    <meta name="description" content="快乐货运APP，专业的货运物流平台，提供车货共享、求职招聘、卡友互助等服务。专业·高效·放心">
    <meta name="keywords" content="快乐货运,货运物流,车货共享,求职招聘,卡友互助">
    <link rel="shortcut icon" href="__CDN__/assets/img/favicon.ico"/>
    <link href="__CDN__/assets/css/index.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* 导航栏 */
        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 70px;
        }

        .nav-logo {
            display: flex;
            align-items: center;    
            font-size: 24px;
            font-weight: bold;
            color: #4a90e2;
        }

        .nav-logo i {
            margin-right: 10px;
            font-size: 28px;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        .nav-menu a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav-menu a:hover {
            color: #4a90e2;
        }

        /* 英雄区域 */
        .hero {
            padding: 120px 0 80px;
            text-align: center;
            color: white;
        }

        .hero-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: center;
        }

        .hero-content h1 {
            font-size: 3.5rem;
            margin-bottom: 20px;
            font-weight: bold;
        }

        .hero-slogan {
            font-size: 1.5rem;
            margin-bottom: 20px;
            color: #ffd700;
            font-weight: 600;
        }

        .hero-description {
            font-size: 1.2rem;
            margin-bottom: 40px;
            opacity: 0.9;
            line-height: 1.6;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .hero-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
        }

        .btn {
            padding: 15px 30px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s;
            display: inline-block;
        }

        .btn-primary {
            background: #ffd700;
            color: #333;
        }

        .btn-primary:hover {
            background: #ffed4e;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: transparent;
            color: white;
            border: 2px solid white;
        }

        .btn-secondary:hover {
            background: white;
            color: #333;
        }

        /* 手机模型 */
        .phone-mockup {
            position: relative;
            width: 300px;
            height: 600px;
            background: #333;
            border-radius: 30px;
            padding: 20px;
            margin: 0 auto;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .phone-screen {
            width: 100%;
            height: 100%;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            position: relative;
        }

        .phone-screen::before {
            content: '';
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 6px;
            background: #333;
            border-radius: 3px;
        }

        .app-preview {
            padding: 30px 20px 20px;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }

        .app-logo {
            width: 80px;
            height: 80px;
            background: white;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            color: #4a90e2;
            font-size: 40px;
        }

        .app-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .app-slogan {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 30px;
        }

        .app-features {
            list-style: none;
            text-align: left;
        }

        .app-features li {
            padding: 8px 0;
            font-size: 14px;
        }

        .app-features i {
            margin-right: 10px;
            color: #ffd700;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }

            .hero-container {
                grid-template-columns: 1fr;
                gap: 40px;
                text-align: center;
            }

            .hero-content h1 {
                font-size: 2.5rem;
            }

            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }

            .phone-mockup {
                width: 250px;
                height: 500px;
            }

            .container {
                padding: 0 15px;
            }

            .hero {
                padding: 100px 0 60px;
            }

            /* 下载区域响应式 */
            .download-container {
                display: block !important;
                text-align: center;
            }

            .qr-section {
                margin-bottom: 40px;
            }

            .download-buttons {
                max-width: 400px;
                margin: 0 auto;
            }

            /* 关于我们区域响应式 */
            .about-container {
                display: block !important;
            }

            .about-content {
                margin-bottom: 40px;
                text-align: center;
            }

            .about-image {
                margin-bottom: 40px;
            }

            /* 关于我们的图标区域 */
            .truck-icon {
                width: 100% !important;
                max-width: 300px !important;
                height: 200px !important;
            }

            /* 统计数据网格 */
            .stats-grid {
                grid-template-columns: 1fr !important;
                gap: 20px !important;
            }
        }

        @media (max-width: 480px) {
            .hero-content h1 {
                font-size: 2rem;
            }

            .hero-slogan {
                font-size: 1.2rem;
            }

            .hero-description {
                font-size: 1rem;
            }

            .phone-mockup {
                width: 200px;
                height: 400px;
            }

            .btn {
                padding: 12px 24px;
                font-size: 14px;
            }

            /* 下载按钮响应式 */
            .download-buttons {
                gap: 15px !important;
            }

            .download-buttons a {
                padding: 15px 20px !important;
                font-size: 1rem !important;
            }

            /* 二维码区域 */
            .qr-section div[style*="padding: 30px"] {
                padding: 20px !important;
            }

            /* 关于我们标题 */
            .about-content h2 {
                font-size: 2rem !important;
            }

            /* 统计数据 */
            .stats-grid h3 {
                font-size: 1.5rem !important;
            }

            /* 截图展示区域响应式 */
            section[style*="grid-template-columns: repeat(auto-fit, minmax(280px, 1fr))"] div {
                grid-template-columns: 1fr !important;
            }

            /* 联系我们区域响应式 */
            section[style*="grid-template-columns: repeat(auto-fit, minmax(220px, 1fr))"] {
                grid-template-columns: repeat(2, 1fr) !important;
                gap: 20px !important;
            }
        }

        /* 页脚链接悬停效果 */
        footer a:hover {
            color: #4a90e2 !important;
        }
    </style>
</head>

<body>

    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-truck"></i>
                <span>快乐货运</span>
            </div>
            <ul class="nav-menu">
                <li><a href="#home">首页</a></li>
                <li><a href="#features">功能</a></li>
                <li><a href="#download">下载</a></li>
                <li><a href="#about">关于</a></li>
            </ul>
        </div>
    </nav>

    <!-- 英雄区域 -->
    <section id="home" class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <h1>快乐货运</h1>
                <p class="hero-slogan">专业·高效·放心</p>
                <p class="hero-description">专业的货运物流平台，智能连接货主与司机，提供安全可靠、高效便捷的一站式货运服务解决方案，让每一次运输都更加轻松愉快</p>
                <div class="hero-buttons">
                    <a href="#download" class="btn btn-primary">立即下载</a>
                    <a href="#features" class="btn btn-secondary">了解更多</a>
                </div>
            </div>
            <div class="hero-image">
                <div class="phone-mockup">
                    <div class="phone-screen">
                        <img src="__CDN__/assets/img/Screenshot_20250801_110617_com.kyzjXXFB.cn(1).jpg" alt="快乐货运登录页" style="width: 100%; height: 100%; object-fit: cover; border-radius: 20px;">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 功能特色 -->
    <section id="features" style="padding: 80px 0; background: white;">
        <div class="container">
            <h2 style="text-align: center; font-size: 2.5rem; margin-bottom: 60px; color: #333;">核心功能</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px;">
                <div style="background: white; padding: 40px 30px; border-radius: 20px; text-align: center; box-shadow: 0 10px 30px rgba(0,0,0,0.1); transition: transform 0.3s;">
                    <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; color: white; font-size: 30px;">
                        <i class="fas fa-handshake"></i>
                    </div>
                    <h3 style="font-size: 1.5rem; margin-bottom: 15px; color: #333;">车货共享</h3>
                    <p style="color: #666; line-height: 1.6;">智能匹配货源与车辆，提高运输效率，降低空载率</p>
                </div>
                <div style="background: white; padding: 40px 30px; border-radius: 20px; text-align: center; box-shadow: 0 10px 30px rgba(0,0,0,0.1); transition: transform 0.3s;">
                    <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; color: white; font-size: 30px;">
                        <i class="fas fa-briefcase"></i>
                    </div>
                    <h3 style="font-size: 1.5rem; margin-bottom: 15px; color: #333;">求职招聘</h3>
                    <p style="color: #666; line-height: 1.6;">专业的物流人才招聘平台，连接企业与求职者</p>
                </div>
                <div style="background: white; padding: 40px 30px; border-radius: 20px; text-align: center; box-shadow: 0 10px 30px rgba(0,0,0,0.1); transition: transform 0.3s;">
                    <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; color: white; font-size: 30px;">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 style="font-size: 1.5rem; margin-bottom: 15px; color: #333;">卡友互助</h3>
                    <p style="color: #666; line-height: 1.6;">卡车司机社区，分享经验，互相帮助，共同成长</p>
                </div>
                <div style="background: white; padding: 40px 30px; border-radius: 20px; text-align: center; box-shadow: 0 10px 30px rgba(0,0,0,0.1); transition: transform 0.3s;">
                    <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; color: white; font-size: 30px;">
                        <i class="fas fa-comments"></i>
                    </div>
                    <h3 style="font-size: 1.5rem; margin-bottom: 15px; color: #333;">消息中心</h3>
                    <p style="color: #666; line-height: 1.6;">实时消息推送，点赞评论，系统通知一目了然</p>
                </div>
                <div style="background: white; padding: 40px 30px; border-radius: 20px; text-align: center; box-shadow: 0 10px 30px rgba(0,0,0,0.1); transition: transform 0.3s;">
                    <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; color: white; font-size: 30px;">
                        <i class="fas fa-crown"></i>
                    </div>
                    <h3 style="font-size: 1.5rem; margin-bottom: 15px; color: #333;">会员服务</h3>
                    <p style="color: #666; line-height: 1.6;">VIP会员专享特权，无限发布货源，专属客服支持</p>
                </div>
                <div style="background: white; padding: 40px 30px; border-radius: 20px; text-align: center; box-shadow: 0 10px 30px rgba(0,0,0,0.1); transition: transform 0.3s;">
                    <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; color: white; font-size: 30px;">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 style="font-size: 1.5rem; margin-bottom: 15px; color: #333;">安全保障</h3>
                    <p style="color: #666; line-height: 1.6;">实名认证，交易担保，全程监控，确保运输安全</p>
                </div>
            </div>
        </div>
    </section>

    <!-- APP截图展示 -->
    <section style="padding: 80px 0; background: #f8f9fa;">
        <div class="container">
            <h2 style="text-align: center; font-size: 2.5rem; margin-bottom: 20px; color: #333;">APP功能截图</h2>
            <p style="text-align: center; font-size: 1.2rem; margin-bottom: 60px; color: #666;">体验快乐货运APP的强大功能</p>

            <!-- 截图展示区域 -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px; max-width: 1200px; margin: 0 auto;">

                <!-- 主功能界面截图 -->
                <div style="background: white; border-radius: 20px; padding: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); text-align: center;">
                    <div style="overflow: hidden; border-radius: 15px; margin-bottom: 20px;">
                        <img src="__CDN__/assets/img/Screenshot_20250801_105909_com.kyzjXXFB.cn(1).jpg" alt="主功能界面" style="width: 100%; height: auto; display: block;">
                    </div>
                    <h4 style="color: #333; margin-bottom: 10px;">主功能界面</h4>
                    <p style="color: #666; font-size: 14px;">车货共享、求职招聘、卡友互助等核心功能</p>
                </div>

                <!-- 货源信息截图 -->
                <div style="background: white; border-radius: 20px; padding: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); text-align: center;">
                    <div style="overflow: hidden; border-radius: 15px; margin-bottom: 20px;">
                        <img src="__CDN__/assets/img/Screenshot_20250801_105916_com.kyzjXXFB.cn(1).jpg" alt="货源信息" style="width: 100%; height: auto; display: block;">
                    </div>
                    <h4 style="color: #333; margin-bottom: 10px;">货源信息</h4>
                    <p style="color: #666; font-size: 14px;">实时货源发布，智能匹配推荐</p>
                </div>

                <!-- 货运详情截图 -->
                <div style="background: white; border-radius: 20px; padding: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); text-align: center;">
                    <div style="overflow: hidden; border-radius: 15px; margin-bottom: 20px;">
                        <img src="__CDN__/assets/img/Screenshot_20250801_105930_com.kyzjXXFB.cn(1).jpg" alt="货运详情" style="width: 100%; height: auto; display: block;">
                    </div>
                    <h4 style="color: #333; margin-bottom: 10px;">卡友江湖</h4>
                    <p style="color: #666; font-size: 14px;">分享经验、共同成长</p>
                </div>

                <!-- 消息中心截图 -->
                <div style="background: white; border-radius: 20px; padding: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); text-align: center;">
                    <div style="overflow: hidden; border-radius: 15px; margin-bottom: 20px;">
                        <img src="__CDN__/assets/img/Screenshot_20250801_105936_com.kyzjXXFB.cn.jpg" alt="消息中心" style="width: 100%; height: auto; display: block;">
                    </div>
                    <h4 style="color: #333; margin-bottom: 10px;">消息中心</h4>
                    <p style="color: #666; font-size: 14px;">点赞评论、系统消息一目了然</p>
                </div>

                <!-- 个人中心截图 -->
                <div style="background: white; border-radius: 20px; padding: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); text-align: center;">
                    <div style="overflow: hidden; border-radius: 15px; margin-bottom: 20px;">
                        <img src="__CDN__/assets/img/Screenshot_20250801_105941_com.kyzjXXFB.cn(1).jpg" alt="个人中心" style="width: 100%; height: auto; display: block;">
                    </div>
                    <h4 style="color: #333; margin-bottom: 10px;">个人中心</h4>
                    <p style="color: #666; font-size: 14px;">完善的个人信息和服务管理</p>
                </div>

                <!-- 会员服务截图 -->
                <div style="background: white; border-radius: 20px; padding: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); text-align: center;">
                    <div style="overflow: hidden; border-radius: 15px; margin-bottom: 20px;">
                        <img src="__CDN__/assets/img/Screenshot_20250801_110027_com.kyzjXXFB.cn(1).jpg" alt="会员服务" style="width: 100%; height: auto; display: block;">
                    </div>
                    <h4 style="color: #333; margin-bottom: 10px;">会员服务</h4>
                    <p style="color: #666; font-size: 14px;">VIP专享特权，提升使用体验</p>
                </div>

            </div>
        </div>
    </section>

    <!-- APP下载 -->
    <section id="download" style="padding: 80px 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
        <div class="container">
            <h2 style="text-align: center; font-size: 2.5rem; margin-bottom: 20px;">下载快乐货运APP</h2>
            <p style="text-align: center; font-size: 1.2rem; margin-bottom: 60px; opacity: 0.9;">扫描二维码，立即体验专业货运服务</p>
            <div class="download-container" style="display: grid; grid-template-columns: 1fr 1fr; gap: 60px; align-items: center; max-width: 800px; margin: 0 auto;">
                <div class="qr-section" style="text-align: center;">
                    <div style="background: white; padding: 30px; border-radius: 20px; display: inline-block; margin-bottom: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
                        {if condition="$app_download_qrcode"}
                        <img src="{$app_download_qrcode}" alt="快乐货运APP下载二维码" style="width: 150px; height: 150px; border-radius: 10px; display: block;">
                        {else /}
                        <div style="width: 150px; height: 150px; background: #f0f0f0; border-radius: 10px; display: flex; align-items: center; justify-content: center; color: #666; font-size: 14px;">
                            二维码区域<br>
                            <small>扫码下载APP</small>
                        </div>
                        {/if}
                    </div>
                    <p style="font-size: 1.1rem; font-weight: 600;">扫码下载</p>
                    <p style="font-size: 0.9rem; color: rgba(255,255,255,0.8); margin-top: 10px;">使用微信扫一扫下载APP</p>
                </div>
                <div class="download-buttons" style="display: flex; flex-direction: column; gap: 20px;">
                    <a href="#" style="background: #34c759; color: white; padding: 20px 30px; border-radius: 15px; text-decoration: none; display: flex; align-items: center; gap: 15px; font-size: 1.1rem; font-weight: 600; transition: all 0.3s;">
                        <i class="fab fa-android" style="font-size: 24px;"></i>
                        <span>Android版下载</span>
                    </a>
                    <a href="#" style="background: #007aff; color: white; padding: 20px 30px; border-radius: 15px; text-decoration: none; display: flex; align-items: center; gap: 15px; font-size: 1.1rem; font-weight: 600; transition: all 0.3s;">
                        <i class="fab fa-apple" style="font-size: 24px;"></i>
                        <span>iOS版下载</span>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- 关于我们 -->
    <section id="about" style="padding: 80px 0; background: #f8f9fa;">
        <div class="container">
            <div class="about-container" style="display: grid; grid-template-columns: 1fr 1fr; gap: 60px; align-items: center;">
                <div class="about-content">
                    <h2 style="font-size: 2.5rem; margin-bottom: 30px; color: #333;">关于快乐货运</h2>
                    <p style="font-size: 1.1rem; line-height: 1.8; color: #666; margin-bottom: 40px;">
                        快乐货运致力于打造最专业的货运物流平台，通过先进的技术和优质的服务，为货主和司机提供高效、安全、便捷的货运解决方案。
                    </p>
                    <div class="stats-grid" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 30px;">
                        <div style="text-align: center;">
                            <h3 style="font-size: 2rem; color: #4a90e2; margin-bottom: 10px;">100万+</h3>
                            <p style="color: #666;">注册用户</p>
                        </div>
                        <div style="text-align: center;">
                            <h3 style="font-size: 2rem; color: #4a90e2; margin-bottom: 10px;">50万+</h3>
                            <p style="color: #666;">活跃司机</p>
                        </div>
                        <div style="text-align: center;">
                            <h3 style="font-size: 2rem; color: #4a90e2; margin-bottom: 10px;">1000万+</h3>
                            <p style="color: #666;">成功订单</p>
                        </div>
                    </div>
                </div>
                <div class="about-image" style="text-align: center;">
                    <div class="truck-icon" style="width: 400px; height: 300px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 20px; display: flex; align-items: center; justify-content: center; color: white; font-size: 24px; margin: 0 auto;">
                        <i class="fas fa-truck" style="font-size: 80px;"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 联系我们 -->
    <section style="padding: 80px 0; background: white;">
        <div class="container">
            <h2 style="text-align: center; font-size: 2.5rem; margin-bottom: 60px; color: #333;">联系我们</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(220px, 1fr)); gap: 30px; max-width: 1000px; margin: 0 auto;">
                <div style="text-align: center; padding: 25px;">
                    <div style="width: 60px; height: 60px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; color: white; font-size: 24px;">
                        <i class="fas fa-phone"></i>
                    </div>
                    <h4 style="font-size: 1.2rem; margin-bottom: 10px; color: #333;">客服热线</h4>
                    <p style="color: #666;">{$customer_mobile}</p>
                </div>
                <div style="text-align: center; padding: 25px;">
                    <div style="width: 60px; height: 60px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; color: white; font-size: 24px;">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <h4 style="font-size: 1.2rem; margin-bottom: 10px; color: #333;">邮箱地址</h4>
                    <p style="color: #666;">{$customer_email|default='<EMAIL>'}</p>
                </div>
                <div style="text-align: center; padding: 25px;">
                    <div style="width: 60px; height: 60px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; color: white; font-size: 24px;">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h4 style="font-size: 1.2rem; margin-bottom: 10px; color: #333;">工作时间</h4>
                    <p style="color: #666;">周一至周日 9:00-18:00</p>
                </div>
                <div style="text-align: center; padding: 25px;">
                    <div style="width: 60px; height: 60px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; color: white; font-size: 24px;">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <h4 style="font-size: 1.2rem; margin-bottom: 10px; color: #333;">公司地址</h4>
                    <p style="color: #666;">{$customer_company|default='北京市朝阳区xxx路xxx号'}</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer style="background: #333; color: white; padding: 50px 0 30px; text-align: center;">
        <div class="container">
            <!-- 公司信息 -->
            <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 30px;">
                <i class="fas fa-truck" style="font-size: 28px; margin-right: 12px; color: #4a90e2;"></i>
                <span style="font-size: 24px; font-weight: bold;">快乐货运</span>
            </div>

            <!-- 快速链接 -->
            <div style="display: flex; justify-content: center; gap: 40px; margin-bottom: 30px; flex-wrap: wrap;">
                <a href="#home" style="color: #ccc; text-decoration: none; transition: color 0.3s;">首页</a>
                <a href="#features" style="color: #ccc; text-decoration: none; transition: color 0.3s;">功能介绍</a>
                <a href="#download" style="color: #ccc; text-decoration: none; transition: color 0.3s;">APP下载</a>
                <a href="#about" style="color: #ccc; text-decoration: none; transition: color 0.3s;">关于我们</a>
            </div>

            <!-- 联系信息 -->
            <div style="display: flex; justify-content: center; gap: 30px; margin-bottom: 30px; flex-wrap: wrap; font-size: 14px;">
                <div style="display: flex; align-items: center; color: #ccc;">
                    <i class="fas fa-phone" style="margin-right: 8px; color: #4a90e2;"></i>
                    <span>客服热线：{$customer_mobile}</span>
                </div>
                <div style="display: flex; align-items: center; color: #ccc;">
                    <i class="fas fa-envelope" style="margin-right: 8px; color: #4a90e2;"></i>
                    <span>邮箱：{$customer_email|default='<EMAIL>'}</span>
                </div>
            </div>

            <!-- 分割线 -->
            <div style="height: 1px; background: #555; margin: 30px 0;"></div>

            <!-- 版权信息和备案号 -->
            <div style="font-size: 14px; color: #999; line-height: 1.6;">
                <p style="margin-bottom: 10px;">
                    Copyright © {$site.name|htmlentities} {:date('Y',time())} 版权所有
                </p>
                <p style="margin-bottom: 10px;">
                    <a href="https://beian.miit.gov.cn" target="_blank" style="color: #4a90e2; text-decoration: none;">
                        {$site.beian|htmlentities}
                    </a>
                </p>
                <p style="font-size: 12px; opacity: 0.8;">
                    快乐货运 - 专业的货运物流平台 | 让货运更简单，让运输更高效
                </p>
            </div>
        </div>
    </footer>

    <script>
        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 悬停效果
        document.querySelectorAll('[style*="transition"]').forEach(element => {
            element.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
            });
            element.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    </script>

</body>

</html>
