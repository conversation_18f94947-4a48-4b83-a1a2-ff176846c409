<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use think\Db;
use think\exception\ValidateException;
use think\exception\PDOException;
use Exception;

/**
 * 人车合影认证
 *
 * @icon fa fa-circle-o
 */
class DriverCar extends Backend
{

    /**
     * DriverCar模型对象
     * @var \app\admin\model\DriverCar
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\DriverCar;
        $this->view->assign("statusList", $this->model->getStatusList());
    }



    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        // 如果传入的是user_id，则根据user_id查找记录
        if ($ids && !is_numeric($ids)) {
            $this->error(__('Invalid parameter'));
        }

        // 优先根据user_id查找，如果没有则用ids
        $userId = $this->request->get('user_id') ?: $ids;
        if ($userId) {
            $row = $this->model->where('user_id', $userId)->find();
        } else {
            $row = $this->model->get($ids);
        }

        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                $result = false;
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                        $row->validateFailException(true)->validate($validate);
                    }

                    // 记录原状态
                    $oldStatus = $row->status;

                    $result = $row->allowField(true)->save($params);

                    // 如果状态发生变化，同步更新司机认证状态表
                    if (isset($params['status']) && $params['status'] != $oldStatus) {
                        \app\common\service\DriverAuthService::handleAuthStatusChange('driver_car', $row->user_id, $params['status']);
                    }

                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


}
