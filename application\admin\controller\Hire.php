<?php

namespace app\admin\controller;

use app\common\controller\Backend;

/**
 * 招聘管理
 *
 * @icon fa fa-circle-o
 */
class Hire extends Backend
{

    /**
     * Hire模型对象
     * @var \app\admin\model\Hire
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Hire;
        $this->view->assign("kindList", $this->model->getKindList());
    }



    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams(null,true);

            $list = $this->model
                    ->with(['user', 'hireCar', 'hireLicense', 'hireExperience'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                // 用户信息
                if ($row->user) {
                    $row->getRelation('user')->visible(['id', 'nickname', 'avatar']);
                }

                // 车型信息
                if ($row->hireCar) {
                    $row['hire_car_name'] = $row->hireCar->name;
                } else {
                    $row['hire_car_name'] = '';
                }

                // 驾照信息
                if ($row->hireLicense) {
                    $row['hire_license_name'] = $row->hireLicense->name;
                } else {
                    $row['hire_license_name'] = '';
                }

                // 经验信息
                if ($row->hireExperience) {
                    $row['hire_experience_name'] = $row->hireExperience->name;
                } else {
                    $row['hire_experience_name'] = '';
                }

                // 处理多选字段 - 岗位福利
                if ($row->hire_post_ids) {
                    $postIds = explode(',', $row->hire_post_ids);
                    $posts = \app\admin\model\HirePost::whereIn('id', $postIds)->column('name');
                    $row['hire_post_names'] = implode(', ', $posts);
                } else {
                    $row['hire_post_names'] = '';
                }

                // 处理多选字段 - 技能
                if ($row->hire_skills_ids) {
                    $skillIds = explode(',', $row->hire_skills_ids);
                    $skills = \app\admin\model\HireSkills::whereIn('id', $skillIds)->column('name');
                    $row['hire_skills_names'] = implode(', ', $skills);
                } else {
                    $row['hire_skills_names'] = '';
                }

                // 格式化时间
                $row['createtime_text'] = date('Y-m-d H:i:s', $row->createtime);

                // 状态文本 - 移除硬编码，使用模型的自动属性
                // $row['kind_text'] 已经通过模型的 getKindTextAttr 方法自动处理
                $row['post_text'] = $row->post == 'repair' ? '机修工' : '补胎工';

                // 清理关联数据，避免输出过多数据
                unset($row->hireCar, $row->hireLicense, $row->hireExperience);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

}
