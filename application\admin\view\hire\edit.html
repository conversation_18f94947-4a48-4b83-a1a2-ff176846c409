<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Kind')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-kind" data-rule="required" class="form-control selectpicker" name="row[kind]">
                {foreach name="kindList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.kind"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Post')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-post" class="form-control" name="row[post]" type="text" value="{$row.post|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Username')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-username" class="form-control" name="row[username]" type="text" value="{$row.username|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Mobile')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-mobile" class="form-control" name="row[mobile]" type="text" value="{$row.mobile|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Num')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-num" data-rule="required" class="form-control" name="row[num]" type="number" value="{$row.num|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Salary_range')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-salary_range" class="form-control datetimerange" data-locale='{"format":"YYYY-MM-DD HH:mm:ss"}' name="row[salary_range]" type="text" value="{$row.salary_range|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Hire_car_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-hire_car_id" data-rule="required" data-source="hire/car/index" class="form-control selectpage" name="row[hire_car_id]" type="text" value="{$row.hire_car_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('License')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-license" class="form-control" name="row[license]" type="text" value="{$row.license|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Address')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-address" class="form-control" name="row[address]" type="text" value="{$row.address|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Post_benefits_ids')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-post_benefits_ids" data-rule="required" data-source="post/benefits/index" data-multiple="true" class="form-control selectpage" name="row[post_benefits_ids]" type="text" value="{$row.post_benefits_ids|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Work_image')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-work_image" class="form-control" size="50" name="row[work_image]" type="text" value="{$row.work_image|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-work_image" class="btn btn-danger faupload" data-input-id="c-work_image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-work_image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-work_image" class="btn btn-primary fachoose" data-input-id="c-work_image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-work_image"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-work_image"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Car_image')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-car_image" class="form-control" size="50" name="row[car_image]" type="text" value="{$row.car_image|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-car_image" class="btn btn-danger faupload" data-input-id="c-car_image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-car_image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-car_image" class="btn btn-primary fachoose" data-input-id="c-car_image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-car_image"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-car_image"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Content')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-content" class="form-control editor" rows="5" name="row[content]" cols="50">{$row.content|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Experience')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-experience" class="form-control" name="row[experience]" type="text" value="{$row.experience|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Skills')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-skills" class="form-control" name="row[skills]" type="text" value="{$row.skills|htmlentities}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
