define(['jquery', 'bootstrap', 'backend', 'addtabs', 'table', 'echarts', 'echarts-theme', 'template'], function ($, undefined, Backend, Datatable, Table, Echarts, undefined, Template) {

    var Controller = {
        index: function () {
            // 基于准备好的dom，初始化echarts实例
            var myChart = Echarts.init(document.getElementById('echart'), 'walden');

            // 获取图表数据
            Controller.loadChartData(myChart);

            $(window).resize(function () {
                myChart.resize();
            });

            $(document).on("click", ".btn-refresh", function () {
                setTimeout(function () {
                    myChart.resize();
                }, 0);
            });
        },

        loadChartData: function(myChart) {
            // 通过AJAX获取图表数据
            $.ajax({
                url: 'dashboard/getChartData',
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.code === 1) {
                        var chartData = response.data;
                        Controller.renderChart(myChart, chartData.column, chartData.userdata);
                    } else {
                        // 如果获取数据失败，使用默认数据
                        Controller.renderChart(myChart, [], []);
                    }
                },
                error: function() {
                    // 如果请求失败，使用默认数据
                    var defaultDates = [];
                    var defaultData = [];
                    for (var i = 6; i >= 0; i--) {
                        var date = new Date();
                        date.setDate(date.getDate() - i);
                        defaultDates.push((date.getMonth() + 1) + '-' + date.getDate());
                        defaultData.push(Math.floor(Math.random() * 50)); // 随机数据作为示例
                    }
                    Controller.renderChart(myChart, defaultDates, defaultData);
                }
            });
        },

        renderChart: function(myChart, columnData, userData) {
            // 指定图表的配置项和数据
            var option = {
                title: {
                    text: '',
                    subtext: ''
                },
                color: [
                    "#4a90e2",
                    "#2ecc71",
                    "#e74c3c",
                    "#9b59b6",
                    "#f39c12",
                    "#17a2b8"
                ],
                tooltip: {
                    trigger: 'axis',
                    formatter: function(params) {
                        return params[0].name + '<br/>' +
                               params[0].seriesName + ': ' + params[0].value + '人';
                    }
                },
                legend: {
                    data: [__('Register user')]
                },
                toolbox: {
                    show: false,
                    feature: {
                        magicType: {show: true, type: ['stack', 'tiled']},
                        saveAsImage: {show: true}
                    }
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: columnData,
                    axisLabel: {
                        textStyle: {
                            color: '#666'
                        }
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLabel: {
                        textStyle: {
                            color: '#666'
                        }
                    }
                },
                grid: [{
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                }],
                series: [{
                    name: __('Register user'),
                    type: 'line',
                    smooth: true,
                    areaStyle: {
                        normal: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [{
                                    offset: 0, color: 'rgba(74, 144, 226, 0.3)'
                                }, {
                                    offset: 1, color: 'rgba(74, 144, 226, 0.1)'
                                }]
                            }
                        }
                    },
                    lineStyle: {
                        normal: {
                            width: 2,
                            color: '#4a90e2'
                        }
                    },
                    itemStyle: {
                        normal: {
                            color: '#4a90e2'
                        }
                    },
                    data: userData
                }]
            };

            // 使用刚指定的配置项和数据显示图表
            myChart.setOption(option);
        }
    };

    return Controller;
});
