<?php

namespace app\api\controller;

use app\common\controller\Api;
use addons\epay\library\Service;
use think\Db;

/**
 * VIP会员接口
 */
class Vip extends Api
{
    protected $noNeedLogin = ['notifyx'];
    protected $noNeedRight = '*';

    /**
     * 创建VIP充值订单
     *
     * @ApiMethod (POST)
     * @ApiParams (name="vip_id", type="int", required=true, description="VIP套餐ID")
     * @ApiParams (name="pay_type", type="string", required=true, description="支付方式:wechat=微信,alipay=支付宝,balance=余额")
     */
    public function createOrder()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }
        $vip_id = $this->request->post('vip_id');
        // 验证必填参数
        if (!$vip_id) {
            $this->error(__('Invalid parameters'));
        }
        // 查询VIP套餐信息
        $vip = Db::name('vip')->where('id', $vip_id)->find();
        if (!$vip) {
            $this->error('VIP套餐不存在');
        }

        // 检查是否已存在未支付的相同VIP套餐订单
        $existingOrder = Db::name('vip_order')
            ->where('user_id', $user->id)
            ->where('vip_id', $vip_id)
            ->where('status', 'pending')
            ->find();

        if ($existingOrder) {
            $this->success('订单已存在', [
                'order_id' => $existingOrder['id'],
                'orderid' => $existingOrder['orderid'],
                'vip_price' => $existingOrder['vip_price'],
                'status' => $existingOrder['status']
            ]);
        }

        // 生成订单号
        $orderid = 'VIP' . date('YmdHis') . rand(1000, 9999);
        $vip_price = $vip['price'];
        if (!$vip_price || $vip_price <= 0) {
            $vip_price = 0.01;
        }
        // 创建订单数据
        $orderData = [
            'orderid' => $orderid,
            'user_id' => $user->id,
            'vip_id' => $vip_id,
            'vip_name' => $vip['name'],
            'vip_day' => $vip['day'],
            'vip_price' => $vip['price'],
            'status' => 'pending',
            'createtime' => time()
        ];
        try {
            Db::startTrans();
            // 插入订单
            $order_id = Db::name('vip_order')->insertGetId($orderData);
            Db::commit();
            // 返回订单信息，前端可以调用支付接口
        } catch (\Exception $e) {
            Db::rollback();
            $this->error('订单创建失败，请重试');
        }
        $this->success('订单创建成功', [
            'order_id' => $order_id,
            'orderid' => $orderid,
            'vip_price' => $vip['price'],
            'amount' => $vip['price'],
            'status' => 'pending'
        ]);
    }

    /**
     * 支付
     *
     * @ApiMethod (POST)
     * @ApiParams (name="orderid", type="string", required=true, description="订单号")
     * @ApiParams (name="pay_type", type="string", required=true, description="支付方式:wechat=微信,alipay=支付宝")
     * @ApiParams (name="test", type="int", required=false, description="测试模式:1=测试,0=正式")
     */
    public function pay()
    {
        $orderid = $this->request->post('orderid');
        $pay_type = $this->request->post('pay_type');
        $test = $this->request->post('test', 0);

        // 验证必填参数
        if (!$orderid || !$pay_type) {
            $this->error(__('Invalid parameters'));
        }
        // 验证支付方式
        if (!in_array($pay_type, ['wechat', 'alipay'])) {
            $this->error('支付方式参数错误');
        }
        // 查询订单信息
        $order = Db::name('vip_order')->where('orderid', $orderid)->find();
        if (!$order) {
            $this->error('订单不存在');
        }
        // 检查订单状态
        if ($order['status'] == 'paid') {
            $this->error('订单已支付');
        }

        // 重新生成订单号（防止第三方支付重复提交）
        $newOrderid = 'VIP' . date('YmdHis') . rand(1000, 9999);

        try {
            // 更新订单号
            Db::name('vip_order')->where('id', $order['id'])->update([
                'orderid' => $newOrderid
            ]);
            $order['orderid'] = $newOrderid; // 更新本地变量
        } catch (\Exception $e) {
            $this->error('订单更新失败，请重试');
        }

        // 测试模式直接回调
        if ($test) {
            $this->payCallback($order['orderid'], '88888888', $pay_type);
            $this->success('支付成功');
        }

        // 微信支付需要openid
        if ($pay_type == 'wechat' && !$openid = $this->auth->openid) {
            $this->error('请先绑定微信');
        }

        $notifyurl = $this->request->domain() . '/api/vip/notifyx/paytype/' . $pay_type;

        // 调用支付接口
        try {
            $pay = Service::submitOrder($order['vip_price'], $order['orderid'], $pay_type, 'vip充值', $notifyurl, null, 'app', $openid ?? '');
        } catch (\Exception $e) {
            $this->error('支付失败，请重试');
        }

        if ($pay) {
            $this->success('支付成功', $pay);
        } else {
            $this->error('支付失败');
        }
    }

    /**
     * 支付成功，仅供开发测试
     */
    public function notifyx()
    {
        $paytype = $this->request->param('paytype');
        $pay = Service::checkNotify($paytype);
        if (!$pay) {
            return json(['code' => 'FAIL', 'message' => '失败'], 500, ['Content-Type' => 'application/json']);
        }
        // 获取回调数据，V3和V2的回调接收不同
        $data = Service::isVersionV3() ? $pay->callback() : $pay->verify();
        try {
            //微信支付V3返回和V2不同
            if (Service::isVersionV3() && $paytype === 'wechat') {
                $data = $data['resource']['ciphertext'];
                $data['total_fee'] = $data['amount']['total'];
            }
            \think\Log::record($data);
            //获取支付金额、订单号
            $payamount = $paytype == 'alipay' ? $data['total_amount'] : $data['total_fee'] / 100;
            $out_trade_no = $data['out_trade_no'];
            $this->payCallback($out_trade_no, $data['transaction_id'], $paytype);
            \think\Log::record("回调成功，订单号：{$out_trade_no}，金额：{$payamount}");
            //你可以在此编写订单逻辑
        } catch (\Exception $e) {
            \think\Log::record("回调逻辑处理错误:" . $e->getMessage(), "error");
        }

        //下面这句必须要执行,且在此之前不能有任何输出
        if (Service::isVersionV3()) {
            return $pay->success()->getBody()->getContents();
        } else {
            return $pay->success()->send();
        }
    }

    /**
     * 支付回调处理
     *
     * @ApiMethod (POST)
     * @ApiParams (name="orderid", type="string", required=true, description="订单号")
     * @ApiParams (name="trade_no", type="string", required=true, description="第三方交易号")
     * @ApiParams (name="pay_type", type="string", required=true, description="支付方式")
     */
    public function payCallback($orderid, $trade_no, $pay_type)
    {
        // 验证必填参数
        if (!$orderid || !$trade_no || !$pay_type) {
            $this->error(__('Invalid parameters'));
        }
        // 查询订单
        $order = Db::name('vip_order')->where('orderid', $orderid)->find();
        if (!$order) {
            $this->error('订单不存在');
        }
        // 检查订单状态
        if ($order['status'] == 'paid') {
            $this->success('订单已支付');
        }
        try {
            Db::startTrans();
            // 更新订单状态
            Db::name('vip_order')->where('id', $order['id'])->update([
                'status' => 'paid',
                'paytime' => time(),
                'paytype' => $pay_type,
                'transid' => $trade_no
            ]);
            // 增加VIP天数
            $this->addVipDays($order['user_id'], $order['vip_day']);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->error('支付处理失败');
        }
        $this->success('支付成功，VIP已开通');
    }

    /**
     * 增加用户VIP天数
     *
     * @param int $user_id 用户ID
     * @param int $days 增加的天数
     */
    private function addVipDays($user_id, $days)
    {
        $user = Db::name('user')->where('id', $user_id)->find();
        if (!$user) {
            throw new \Exception('用户不存在');
        }
        $currentTime = time();
        $newEndtime = 0;
        // 如果用户当前是VIP且未过期，在原有基础上增加天数
        if ($user['is_vip'] == 1 && $user['endtime'] > $currentTime) {
            $newEndtime = $user['endtime'] + ($days * 86400);
        } else {
            // 如果不是VIP或已过期，从当前时间开始计算
            $newEndtime = $currentTime + ($days * 86400);
        }

        // 更新用户VIP信息
        Db::name('user')->where('id', $user_id)->update([
            'is_vip' => 1,
            'endtime' => $newEndtime
        ]);
    }
}
