<?php

namespace app\api\controller;

use addons\epay\library\Service;
use app\common\controller\Api;
use think\Db;

/**
 * 企业认证接口
 */
class Company extends Api
{
    protected $noNeedLogin = ['notifyx'];
    protected $noNeedRight = '*';

    /**
     * 企业认证申请
     *
     * @ApiMethod (POST)
     * @ApiParams (name="company_category_id", type="int", required=false, description="企业分类ID")
     * @ApiParams (name="license_image", type="string", required=true, description="营业执照照片")
     * @ApiParams (name="name", type="string", required=true, description="公司名称")
     * @ApiParams (name="code", type="string", required=true, description="统一社会信用代码")
     * @ApiParams (name="front_image", type="string", required=true, description="法人身份证正面")
     * @ApiParams (name="reverse_image", type="string", required=true, description="法人身份证反面")
     * @ApiParams (name="legal_name", type="string", required=true, description="法人姓名")
     * @ApiParams (name="legal_card", type="string", required=true, description="法人身份证号")
     */
    public function apply()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $company_category_id = $this->request->post('company_category_id');
        $license_image = $this->request->post('license_image');
        $name = $this->request->post('name');
        $code = $this->request->post('code');
        $front_image = $this->request->post('front_image');
        $reverse_image = $this->request->post('reverse_image');
        $legal_name = $this->request->post('legal_name');
        $legal_card = $this->request->post('legal_card');

        // 验证必填参数
        if (!$license_image || !$name || !$code || !$front_image || !$reverse_image || !$legal_name || !$legal_card) {
            $this->error(__('Invalid parameters'));
        }

        // 验证统一社会信用代码格式（18位）
        if (!preg_match('/^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/', $code)) {
            $this->error('统一社会信用代码格式不正确');
        }

        // 验证身份证号格式
        if (!preg_match('/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/', $legal_card)) {
            $this->error('法人身份证号格式不正确');
        }

        // 检查是否已有审核记录
        $existCompany = Db::name('company')->where('user_id', $user->id)->find();
        if ($existCompany) {
            if ($existCompany['status'] == 'pending') {
                $this->error('您的企业认证正在审核中，请耐心等待');
            } elseif ($existCompany['status'] == 'pass') {
                $this->error('您已通过企业认证，无需重复申请');
            } elseif ($existCompany['status'] == 'refuse') {
                $this->error('您的企业认证已被拒绝，请使用编辑后重新提交');
            }
        }

        // 检查统一社会信用代码是否已被使用
        $codeExists = Db::name('company')->where('code', $code)->where('status', 'pass')->find();
        if ($codeExists) {
            $this->error('该统一社会信用代码已被使用');
        }

        $data = [
            'user_id' => $user->id,
            'company_category_id' => $company_category_id,
            'license_image' => $license_image,
            'name' => $name,
            'code' => $code,
            'front_image' => $front_image,
            'reverse_image' => $reverse_image,
            'legal_name' => $legal_name,
            'legal_card' => $legal_card,
            'status' => 'pending',
            'createtime' => time()
        ];

        try {
            $id = Db::name('company')->insertGetId($data);
        } catch (\Exception $e) {
            $this->error('申请提交失败，请重试');
        }
        $this->success('企业认证申请提交成功，请等待审核', ['id' => $id]);
    }

    /**
     * 企业认证编辑（仅审核拒绝后可用）
     *
     * @ApiMethod (POST)
     * @ApiParams (name="company_category_id", type="int", required=false, description="企业分类ID")
     * @ApiParams (name="license_image", type="string", required=true, description="营业执照照片")
     * @ApiParams (name="name", type="string", required=true, description="公司名称")
     * @ApiParams (name="code", type="string", required=true, description="统一社会信用代码")
     * @ApiParams (name="front_image", type="string", required=true, description="法人身份证正面")
     * @ApiParams (name="reverse_image", type="string", required=true, description="法人身份证反面")
     * @ApiParams (name="legal_name", type="string", required=true, description="法人姓名")
     * @ApiParams (name="legal_card", type="string", required=true, description="法人身份证号")
     */
    public function edit()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        // 检查是否有审核记录
        $company = Db::name('company')->where('user_id', $user->id)->find();
        if (!$company) {
            $this->error('您还没有企业认证记录，请先申请');
        }

        // 只有审核拒绝的记录才能编辑
        if ($company['status'] != 'refuse') {
            $this->error('只有审核拒绝的记录才能重新编辑');
        }

        $company_category_id = $this->request->post('company_category_id');
        $license_image = $this->request->post('license_image');
        $name = $this->request->post('name');
        $code = $this->request->post('code');
        $front_image = $this->request->post('front_image');
        $reverse_image = $this->request->post('reverse_image');
        $legal_name = $this->request->post('legal_name');
        $legal_card = $this->request->post('legal_card');

        // 验证必填参数
        if (!$license_image || !$name || !$code || !$front_image || !$reverse_image || !$legal_name || !$legal_card) {
            $this->error(__('Invalid parameters'));
        }

        // 验证统一社会信用代码格式
        if (!preg_match('/^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/', $code)) {
            $this->error('统一社会信用代码格式不正确');
        }

        // 验证身份证号格式
        if (!preg_match('/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/', $legal_card)) {
            $this->error('法人身份证号格式不正确');
        }

        // 如果修改了统一社会信用代码，检查是否已被其他用户使用
        if ($code != $company['code']) {
            $codeExists = Db::name('company')->where('code', $code)->where('status', 'pass')->find();
            if ($codeExists) {
                $this->error('该统一社会信用代码已被使用');
            }
        }

        $data = [
            'company_category_id' => $company_category_id,
            'license_image' => $license_image,
            'name' => $name,
            'code' => $code,
            'front_image' => $front_image,
            'reverse_image' => $reverse_image,
            'legal_name' => $legal_name,
            'legal_card' => $legal_card,
            'refuse_remark' => '',
            'status' => 'pending' // 重新提交后状态改为审核中
        ];

        try {
            Db::name('company')->where('id', $company['id'])->update($data);
        } catch (\Exception $e) {
            $this->error('修改失败，请重试');
        }
        $this->success('企业认证信息修改成功，请等待重新审核');
    }

    /**
     * 获取企业认证详情
     *
     * @ApiMethod (GET)
     */
    public function info()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        // 查询企业认证信息
        $company = Db::name('company')->where('user_id', $user->id)->find();

        $result = [
            'has_apply' => false,
            'company' => null
        ];

        if ($company) {
            $result['has_apply'] = true;
            $result['company'] = $company;
        }
        $this->success('获取成功', $result);
    }


    /**
     * 商家入驻申请
     *
     * @ApiMethod (POST)
     * @ApiParams (name="category_id", type="int", required=true, description="商家分类ID")
     * @ApiParams (name="name", type="string", required=true, description="店铺名称")
     * @ApiParams (name="image", type="string", required=true, description="封面图")
     * @ApiParams (name="images", type="string", required=false, description="轮播图，多张用逗号分隔")
     * @ApiParams (name="address", type="string", required=true, description="地址")
     * @ApiParams (name="lng", type="string", required=true, description="经度")
     * @ApiParams (name="lat", type="string", required=true, description="纬度")
     * @ApiParams (name="mobile", type="string", required=true, description="联系电话")
     * @ApiParams (name="content", type="string", required=false, description="店铺描述")
     * @ApiParams (name="extra_data", type="string", required=false, description="额外数据JSON格式")
     */
    public function storeApply()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        // 检查是否已通过企业认证
        $company = Db::name('company')->where('user_id', $user->id)->where('status', 'pass')->find();
        if (!$company) {
            $this->error('请先通过企业认证后再申请商家入驻');
        }

        $category_id = $company['company_category_id'];
        $name = $this->request->post('name');
        $image = $this->request->post('image');
        $images = $this->request->post('images', '');
        $address = $this->request->post('address');
        $province = $this->request->post('province');
        $city = $this->request->post('city');
        $lng = $this->request->post('lng');
        $lat = $this->request->post('lat');
        $mobile = $this->request->post('mobile');
        $content = $this->request->post('content', '');
        $extra_data = $this->request->post('extra_data', '');
        // 验证必填参数
        if (!$category_id || !$name || !$image || !$address || !$lng || !$lat || !$mobile) {
            $this->error(__('Invalid parameters'));
        }

        // 验证手机号格式
        if (!preg_match('/^1[3-9]\d{9}$/', $mobile)) {
            $this->error('手机号格式不正确');
        }

        // 获取商家分类信息
        $category = Db::name('company_category')->where('id', $category_id)->find();
        if (!$category) {
            $this->error('商家分类不存在');
        }

        // 检查用户是否已经入驻过任何类型的商家
        $existingStores = [];
        $categoryList = Db::name('company_category')->select();
        foreach ($categoryList as $cat) {
            if ($cat['code']) {
                $exists = Db::name($cat['code'])->where('user_id', $user->id)->find();
                if ($exists) {
                    $existingStores[] = $cat['name'];
                }
            }
        }

        if (!empty($existingStores)) {
            $this->error('您已入驻了' . implode('、', $existingStores) . '，一个用户只能入驻一种类型的商家');
        }
        // 生成订单号
        $orderid = 'STORE' . date('YmdHis') . rand(1000, 9999);
        $configAmount = config('site.store_amount');
        if (!$configAmount || $configAmount <= 0) {
            $configAmount = 0.01;
        }
        // 基础数据
        $baseData = [
            'user_id' => $user->id,
            'name' => $name,
            'image' => $image,
            'images' => $images,
            'address' => $address,
            'province' => $province,
            'city' => $city,
            'lng' => $lng,
            'lat' => $lat,
            'mobile' => $mobile,
            'content' => $content,
            'orderid' => $orderid,
            'status' => 'pending',
            'amount' => $configAmount, // 入驻费用，可以从配置中读取
            'createtime' => time()
        ];

        // 根据不同类型添加特殊字段
        $storeData = $this->buildStoreData($category['code'], $baseData, $extra_data);

        try {
            // 插入到对应的表中
            $store_id = Db::name($category['code'])->insertGetId($storeData);
        } catch (\Exception $e) {
            $this->error('申请提交失败，请重试' . $e->getMessage());
        }

        $this->success('商家入驻申请提交成功，请完成支付', [
            'store_id' => $store_id,
            'orderid' => $orderid,
            'category_name' => $category['name'],
            'amount' => $baseData['amount']
        ]);
    }

    /**
     * 根据商家类型构建数据
     */
    private function buildStoreData($categoryCode, $baseData, $extraData = '')
    {
        $data = $baseData;
        // 解析额外数据
        if (needHtmlDecode($extraData)) {
            $extraData = htmlspecialchars_decode($extraData);
        }
        $extra = [];
        if ($extraData) {
            $extra = json_decode($extraData, true) ?: [];
        }
        switch ($categoryCode) {
            case 'repair': // 附近修理
            case 'tarpaulin':
                $data['repair_car_ids'] = $extra['repair_car_ids'] ?? '';
                $data['repair_range_ids'] = $extra['repair_range_ids'] ?? '';
                break;
            case 'tire': // 流动补胎
                $data['brand_ids'] = $extra['brand_ids'] ?? '';
                break;
            case 'accessory': // 买配件
                $data['brand_ids'] = $extra['brand_ids'] ?? '';
                $data['range_ids'] = $extra['range_ids'] ?? '';
                break;
            case 'car': // 二手车交易
                $data['car_type_id'] = $extra['car_type_id'] ?? '';
                $data['is_gua'] = $extra['is_gua'] ?? 0;
                $data['fuel_type'] = $extra['fuel_type'] ?? '';
                $data['supply_type'] = $extra['supply_type'] ?? '';
                $data['horsepower'] = $extra['horsepower'] ?? '';
                $data['engine'] = $extra['engine'] ?? '';
                $data['drive_mode'] = $extra['drive_mode'] ?? '';
                $data['size'] = $extra['size'] ?? '';
                $data['emission'] = $extra['emission'] ?? '';
                $data['age'] = $extra['age'] ?? '';
                $data['price'] = $extra['price'] ?? '';
                break;
                // server, inspection, crane 等其他类型使用基础数据即可
        }
        return $data;
    }

    /**
     * 商家入驻支付
     *
     * @ApiMethod (POST)
     * @ApiParams (name="orderid", type="string", required=true, description="订单号")
     * @ApiParams (name="pay_type", type="string", required=true, description="支付方式:wechat=微信,alipay=支付宝")
     */
    public function pay()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $orderid = $this->request->post('orderid');
        $pay_type = $this->request->post('pay_type');
        $test = $this->request->post('test', 0);
        if (!$orderid || !$pay_type) {
            $this->error(__('Invalid parameters'));
        }

        if (!in_array($pay_type, ['wechat', 'alipay'])) {
            $this->error('支付方式参数错误');
        }

        // 查找订单（需要在所有商家表中查找）
        $order = null;
        $tableName = '';
        $categoryList = Db::name('company_category')->select();

        foreach ($categoryList as $category) {
            if ($category['code']) {
                $found = Db::name($category['code'])
                    ->where('orderid', $orderid)
                    ->where('user_id', $user->id)
                    ->find();
                if ($found) {
                    $order = $found;
                    $tableName = $category['code'];
                    break;
                }
            }
        }

        if (!$order) {
            $this->error('订单不存在');
        }

        if ($order['status'] == 'paid') {
            $this->error('订单已支付');
        }

        // 重新生成订单号（防止第三方支付重复提交）
        $newOrderid = 'STORE' . date('YmdHis') . rand(1000, 9999);

        try {
            // 更新订单号
            Db::name($tableName)->where('id', $order['id'])->update([
                'orderid' => $newOrderid
            ]);
            $orderid = $newOrderid; // 更新本地变量
        } catch (\Exception $e) {
            $this->error('订单更新失败，请重试');
        }

        if ($test) {
            $this->storePayCallback($orderid, '88888888', $pay_type);
            $this->success('支付订单创建成功');
        }
        if ($pay_type == 'wechat' && !$openid = $this->auth->openid) {
            $this->error('请先绑定微信');
        }
        $notifyurl = $this->request->domain() . '/api/company/notifyx/paytype/' . $pay_type;
        // 调用支付接口（参考VIP支付）
        try {
            $payResult = Service::submitOrder($order['amount'], $orderid, $pay_type, '商家入驻费用', $notifyurl, null, 'app', $openid ?? '');
        } catch (\Exception $e) {
            $this->error('支付失败，请重试');
        }
        $this->success('支付订单创建成功', $payResult);
    }


    /**
     * 支付成功，仅供开发测试
     */
    public function notifyx()
    {
        $paytype = $this->request->param('paytype');
        $pay = Service::checkNotify($paytype);
        if (!$pay) {
            return json(['code' => 'FAIL', 'message' => '失败'], 500, ['Content-Type' => 'application/json']);
        }
        // 获取回调数据，V3和V2的回调接收不同
        $data = Service::isVersionV3() ? $pay->callback() : $pay->verify();
        try {
            //微信支付V3返回和V2不同
            if (Service::isVersionV3() && $paytype === 'wechat') {
                $data = $data['resource']['ciphertext'];
                $data['total_fee'] = $data['amount']['total'];
            }
            \think\Log::record($data);
            //获取支付金额、订单号
            $payamount = $paytype == 'alipay' ? $data['total_amount'] : $data['total_fee'] / 100;
            $out_trade_no = $data['out_trade_no'];
            $this->storePayCallback($out_trade_no, $data['transaction_id'], $paytype);
            \think\Log::record("回调成功，订单号：{$out_trade_no}，金额：{$payamount}");
            //你可以在此编写订单逻辑
        } catch (\Exception $e) {
            \think\Log::record("回调逻辑处理错误:" . $e->getMessage(), "error");
        }

        //下面这句必须要执行,且在此之前不能有任何输出
        if (Service::isVersionV3()) {
            return $pay->success()->getBody()->getContents();
        } else {
            return $pay->success()->send();
        }
    }
    /**
     * 商家入驻支付回调
     *
     * @ApiMethod (POST)
     * @ApiParams (name="orderid", type="string", required=true, description="订单号")
     * @ApiParams (name="trade_no", type="string", required=true, description="第三方交易号")
     * @ApiParams (name="pay_type", type="string", required=true, description="支付方式")
     */
    public function storePayCallback($orderid, $trade_no, $pay_type)
    {
        if (!$orderid || !$trade_no || !$pay_type) {
            $this->error(__('Invalid parameters'));
        }

        // 查找订单
        $order = null;
        $tableName = '';
        $categoryList = Db::name('company_category')->select();

        foreach ($categoryList as $category) {
            if ($category['code']) {
                $found = Db::name($category['code'])->where('orderid', $orderid)->find();
                if ($found) {
                    $order = $found;
                    $tableName = $category['code'];
                    break;
                }
            }
        }

        if (!$order) {
            $this->error('订单不存在');
        }

        if ($order['status'] == 'paid') {
            $this->success('订单已支付');
        }

        try {
            // 更新订单状态
            Db::name($tableName)->where('orderid', $orderid)->update([
                'status' => 'paid',
                'paytype' => $pay_type,
                'paytime' => time(),
                'transid' => $trade_no
            ]);
        } catch (\Exception $e) {
            $this->error('支付处理失败');
        }

        $this->success('支付成功，商家入驻完成');
    }

    /**
     * 商家信息编辑（仅审核拒绝后可用）
     *
     * @ApiMethod (POST)
     * @ApiParams (name="name", type="string", required=true, description="店铺名称")
     * @ApiParams (name="image", type="string", required=true, description="封面图")
     * @ApiParams (name="images", type="string", required=false, description="轮播图，多张用逗号分隔")
     * @ApiParams (name="address", type="string", required=true, description="地址")
     * @ApiParams (name="lng", type="string", required=true, description="经度")
     * @ApiParams (name="lat", type="string", required=true, description="纬度")
     * @ApiParams (name="mobile", type="string", required=true, description="联系电话")
     * @ApiParams (name="content", type="string", required=false, description="店铺描述")
     * @ApiParams (name="extra_data", type="string", required=false, description="额外数据JSON格式")
     */
    public function storeEdit()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        // 检查是否已通过企业认证
        $company = Db::name('company')->where('user_id', $user->id)->where('status', 'pass')->find();
        if (!$company) {
            $this->error('请先通过企业认证后再操作商家信息');
        }

        // 查找用户的商家信息
        $storeInfo = null;
        $tableName = '';
        $categoryList = Db::name('company_category')->select();

        foreach ($categoryList as $category) {
            if ($category['code']) {
                $store = Db::name($category['code'])->where('user_id', $user->id)->find();
                if ($store) {
                    $storeInfo = $store;
                    $tableName = $category['code'];
                    break;
                }
            }
        }

        if (!$storeInfo) {
            $this->error('您还没有商家入驻记录，请先申请');
        }

        // 只有审核拒绝的记录才能编辑
        if ($storeInfo['status'] != 'refuse') {
            $this->error('只有审核拒绝的商家信息才能重新编辑');
        }

        $name = $this->request->post('name');
        $image = $this->request->post('image');
        $images = $this->request->post('images', '');
        $address = $this->request->post('address');
        $province = $this->request->post('province');
        $city = $this->request->post('city');
        $lng = $this->request->post('lng');
        $lat = $this->request->post('lat');
        $mobile = $this->request->post('mobile');
        $content = $this->request->post('content', '');
        $extra_data = $this->request->post('extra_data', '');

        // 验证必填参数
        if (!$name || !$image || !$address || !$lng || !$lat || !$mobile) {
            $this->error(__('Invalid parameters'));
        }

        // 验证手机号格式
        if (!preg_match('/^1[3-9]\d{9}$/', $mobile)) {
            $this->error('手机号格式不正确');
        }

        // 基础数据
        $baseData = [
            'name' => $name,
            'image' => $image,
            'images' => $images,
            'address' => $address,
            'province' => $province,
            'city' => $city,
            'lng' => $lng,
            'lat' => $lat,
            'mobile' => $mobile,
            'content' => $content,
            'refuse_remark' => '', // 清空拒绝理由
            'status' => 'pending' // 重新提交后状态改为审核中
        ];

        // 根据不同类型添加特殊字段
        $storeData = $this->buildStoreData($tableName, $baseData, $extra_data);

        try {
            // 更新商家信息
            Db::name($tableName)->where('id', $storeInfo['id'])->update($storeData);
        } catch (\Exception $e) {
            $this->error('修改失败，请重试：' . $e->getMessage());
        }

        $this->success('商家信息修改成功，请等待重新审核');
    }

    /**
     * 获取用户商家入驻状态
     *
     * @ApiMethod (GET)
     */
    public function storeStatus()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        // 检查企业认证状态
        $company = Db::name('company')->where('user_id', $user->id)->find();
        $companyStatus = $company ? $company['status'] : 'none';

        // 检查商家入驻状态
        $storeInfo = null;
        $categoryList = Db::name('company_category')->select();

        foreach ($categoryList as $category) {
            if ($category['code']) {
                $store = Db::name($category['code'])->where('user_id', $user->id)->find();
                if ($store) {
                    $storeInfo = [
                        'category_id' => $category['id'],
                        'category_name' => $category['name'],
                        'category_code' => $category['code'],
                        'store_id' => $store['id'],
                        'store_name' => $store['name'],
                        'status' => $store['status'],
                        'orderid' => $store['orderid'] ?? '',
                        'amount' => $store['amount'] ?? 0,
                        'refuse_remark' => $store['refuse_remark'] ?? '',
                        'can_edit' => $store['status'] == 'refuse' // 只有拒绝状态才能编辑
                    ];
                    break;
                }
            }
        }
        $this->success('获取成功', [
            'company_status' => $companyStatus,
            'store_info' => $storeInfo,
            'can_apply' => $companyStatus == 'pass' && !$storeInfo
        ]);
    }

    /**
     * 获取商家详细信息（用于编辑）
     *
     * @ApiMethod (GET)
     */
    public function storeDetail()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        // 查找用户的商家信息
        $storeInfo = null;
        $tableName = '';
        $categoryInfo = null;
        $categoryList = Db::name('company_category')->select();

        foreach ($categoryList as $category) {
            if ($category['code']) {
                $store = Db::name($category['code'])->where('user_id', $user->id)->find();
                if ($store) {
                    $storeInfo = $store;
                    $tableName = $category['code'];
                    $categoryInfo = $category;
                    break;
                }
            }
        }

        if (!$storeInfo) {
            $this->error('您还没有商家入驻记录');
        }

        // 添加分类信息
        $storeInfo['category_info'] = $categoryInfo;
        $storeInfo['table_name'] = $tableName;

        $this->success('获取成功', $storeInfo);
    }

    /**
     * 获取轮胎列表
     *
     * @ApiMethod (GET)
     * @ApiParams (name="page", type="integer", required=false, description="页码")
     * @ApiParams (name="limit", type="integer", required=false, description="每页数量")
     * @ApiParams (name="brand_ids", type="string", required=false, description="品牌ID")
     * @ApiParams (name="lng", type="string", required=false, description="经度")
     * @ApiParams (name="lat", type="string", required=false, description="纬度")
     */
    public function getTireList()
    {
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);
        $brand_ids = $this->request->get('brand_ids', '');
        $lng = $this->request->get('lng', '');
        $lat = $this->request->get('lat', '');

        $query = \app\admin\model\Tire::with(['user', 'company'])
            ->where('tire.status', 'paid')
            ->where('tire.user_id', '>', 0)
            ->where('company.id', '>', 0);

        // 使用find_in_set查询brand_ids
        if (!empty($brand_ids)) {
            $query->whereRaw("FIND_IN_SET(?, brand_ids)", [$brand_ids]);
        }

        // 如果传入了经纬度，添加距离计算和排序
        if (!empty($lng) && !empty($lat)) {
            // 在SQL中计算距离并排序
            $query->field('tire.*, (6371 * acos(cos(radians(' . $lat . ')) * cos(radians(tire.lat)) * cos(radians(tire.lng) - radians(' . $lng . ')) + sin(radians(' . $lat . ')) * sin(radians(tire.lat)))) AS distance')
                ->order('distance', 'asc');
        } else {
            $query->order('createtime', 'desc');
        }

        $list = $query->paginate($limit, false, ['page' => $page]);

        // 处理每个数据项，确保字段可见
        $list->each(function ($item) use ($lng, $lat) {
            // 如果没有传入经纬度，设置distance为null
            if (empty($lng) || empty($lat)) {
                $item->setAttr('distance', 0);
            } else {
                $item->distance = round($item->distance, 2);
            }
            // 确保distance字段在visible字段列表中
            $item->visible(['id', 'image', 'images', 'images_arr', 'name', 'lng', 'lat', 'address', 'rate', 'rate_num', 'brand_ids', 'distance']);
        });

        $this->success('获取成功', $list);
    }

    /**
     * 获取轮胎详情
     *
     * @ApiMethod (GET)
     * @ApiParams (name="id", type="integer", required=true, description="轮胎ID")
     * @ApiParams (name="lng", type="string", required=false, description="经度")
     * @ApiParams (name="lat", type="string", required=false, description="纬度")
     */
    public function getTireDetail()
    {
        $id = $this->request->get('id');
        $lng = $this->request->get('lng', '');
        $lat = $this->request->get('lat', '');

        if (!$id) {
            $this->error('参数错误');
        }

        $tire = \app\admin\model\Tire::with(['user', 'company'])
            ->where('tire.id', $id)
            ->where('tire.status', 'paid')
            ->where('tire.user_id', '>', 0)
            ->where('company.id', '>', 0)
            ->find();

        if (!$tire) {
            $this->error('数据不存在');
        }

        // 关联查询brand_ids对应的轮胎品牌数据
        if (!empty($tire->brand_ids)) {
            $brand_ids = explode(',', $tire->brand_ids);
            $brands = \app\admin\model\TireBrand::whereIn('id', $brand_ids)->select();
            $tire->brands = $brands;
        } else {
            $tire->brands = [];
        }

        // 如果传入了经纬度，计算距离
        if (!empty($lng) && !empty($lat) && !empty($tire->lng) && !empty($tire->lat)) {
            $tire->distance = $this->calculateDistance($lat, $lng, $tire->lat, $tire->lng);
        } else {
            $tire->distance = 0;
        }

        // 限制返回字段
        $tire->visible(['id', 'image', 'images', 'images_arr', 'name', 'lng', 'lat', 'address', 'rate', 'rate_num', 'brand_ids', 'distance', 'brands']);

        $this->success('获取成功', $tire);
    }

    /**
     * 获取轮胎评价列表
     *
     * @ApiMethod (GET)
     * @ApiParams (name="tire_id", type="integer", required=true, description="轮胎ID")
     * @ApiParams (name="page", type="integer", required=false, description="页码")
     * @ApiParams (name="limit", type="integer", required=false, description="每页数量")
     * @ApiParams (name="type", type="string", required=false, description="评价类型：all=全部，good=好评(4-5星)，bad=差评(1-3星)")
     */
    public function getTireEvaluateList()
    {
        $tire_id = $this->request->get('tire_id');
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);
        $type = $this->request->get('type', 'all');

        if (!$tire_id) {
            $this->error('参数错误');
        }

        $query = \app\admin\model\TireEvaluate::where('tire_id', $tire_id);

        // 根据评价类型筛选
        switch ($type) {
            case 'good':
                $query->where('star', '>=', 4);
                break;
            case 'bad':
                $query->where('star', '<=', 3);
                break;
            case 'all':
            default:
                // 不添加额外条件，显示全部
                break;
        }

        $list = $query->order('weigh', 'desc')
            ->paginate($limit, false, ['page' => $page])->each(function ($item) {
                // 限制返回字段
                $item->visible(['id', 'tire_id', 'star', 'star_text', 'content', 'images', 'nickname', 'avatar', 'createtime']);
                return $item;
            });

        $this->success('获取成功', $list);
    }

    /**
     * 获取维修列表
     *
     * @ApiMethod (GET)
     * @ApiParams (name="page", type="integer", required=false, description="页码")
     * @ApiParams (name="limit", type="integer", required=false, description="每页数量")
     * @ApiParams (name="repair_car_ids", type="string", required=false, description="维修车辆ID")
     * @ApiParams (name="repair_range_ids", type="string", required=false, description="维修范围ID")
     * @ApiParams (name="lng", type="string", required=false, description="经度")
     * @ApiParams (name="lat", type="string", required=false, description="纬度")
     */
    public function getRepairList()
    {
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);
        $repair_car_ids = $this->request->get('repair_car_ids', '');
        $repair_range_ids = $this->request->get('repair_range_ids', '');
        $lng = $this->request->get('lng', '');
        $lat = $this->request->get('lat', '');

        $query = \app\admin\model\Repair::with(['user', 'company'])
            ->where('repair.status', 'paid')
            ->where('repair.user_id', '>', 0)
            ->where('company.id', '>', 0);

        // 使用find_in_set查询repair_car_ids
        if (!empty($repair_car_ids)) {
            $query->whereRaw("FIND_IN_SET(?, repair_car_ids)", [$repair_car_ids]);
        }

        // 使用find_in_set查询repair_range_ids
        if (!empty($repair_range_ids)) {
            $query->whereRaw("FIND_IN_SET(?, repair_range_ids)", [$repair_range_ids]);
        }

        // 如果传入了经纬度，添加距离计算和排序
        if (!empty($lng) && !empty($lat)) {
            $query->field('repair.*, (6371 * acos(cos(radians(' . $lat . ')) * cos(radians(repair.lat)) * cos(radians(repair.lng) - radians(' . $lng . ')) + sin(radians(' . $lat . ')) * sin(radians(repair.lat)))) AS distance')
                ->order('distance', 'asc');
        } else {
            $query->order('createtime', 'desc');
        }

        $list = $query->paginate($limit, false, ['page' => $page])->each(function ($item) use ($lng, $lat) {
            // 如果没有传入经纬度，设置distance为null
            if (empty($lng) || empty($lat)) {
                $item->setAttr('distance', 0);
            } else {
                $item->distance = round($item->distance, 2);
            }
            // 限制返回字段
            $item->visible(['id', 'image', 'images', 'images_arr', 'name', 'lng', 'lat', 'address', 'rate', 'rate_num', 'repair_car_ids', 'distance']);
            return $item;
        });

        $this->success('获取成功', $list);
    }

    /**
     * 获取维修详情
     *
     * @ApiMethod (GET)
     * @ApiParams (name="id", type="integer", required=true, description="维修ID")
     * @ApiParams (name="lng", type="string", required=false, description="经度")
     * @ApiParams (name="lat", type="string", required=false, description="纬度")
     */
    public function getRepairDetail()
    {
        $id = $this->request->get('id');
        $lng = $this->request->get('lng', '');
        $lat = $this->request->get('lat', '');

        if (!$id) {
            $this->error('参数错误');
        }

        $repair = \app\admin\model\Repair::with(['user', 'company'])
            ->where('repair.id', $id)
            ->where('repair.status', 'paid')
            ->where('repair.user_id', '>', 0)
            ->where('company.id', '>', 0)
            ->find();

        if (!$repair) {
            $this->error('数据不存在');
        }

        // 关联查询repair_car_ids对应的车辆类型数据
        if (!empty($repair->repair_car_ids)) {
            $carIds = explode(',', $repair->repair_car_ids);
            $cars = \app\admin\model\RepairCar::whereIn('id', $carIds)->select();
            $repair->repair_cars = $cars;
        } else {
            $repair->repair_cars = [];
        }

        // 关联查询repair_range_ids对应的维修范围数据
        if (!empty($repair->repair_range_ids)) {
            $rangeIds = explode(',', $repair->repair_range_ids);
            $ranges = \app\admin\model\RepairRange::whereIn('id', $rangeIds)->select();
            $repair->repair_ranges = $ranges;
        } else {
            $repair->repair_ranges = [];
        }

        // 如果传入了经纬度，计算距离
        if (!empty($lng) && !empty($lat) && !empty($repair->lng) && !empty($repair->lat)) {
            $repair->distance = $this->calculateDistance($lat, $lng, $repair->lat, $repair->lng);
        } else {
            $repair->distance = 0;
        }

        // 限制返回字段
        $repair->visible(['id', 'image', 'images', 'images_arr', 'name', 'lng', 'lat', 'address', 'rate', 'rate_num', 'repair_car_ids', 'distance', 'repair_ranges', 'repair_cars']);

        $this->success('获取成功', $repair);
    }

    /**
     * 获取维修评价列表
     *
     * @ApiMethod (GET)
     * @ApiParams (name="repair_id", type="integer", required=true, description="维修ID")
     * @ApiParams (name="page", type="integer", required=false, description="页码")
     * @ApiParams (name="limit", type="integer", required=false, description="每页数量")
     * @ApiParams (name="type", type="string", required=false, description="评价类型：all=全部，good=好评(4-5星)，bad=差评(1-3星)")
     */
    public function getRepairEvaluateList()
    {
        $repair_id = $this->request->get('repair_id');
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);
        $type = $this->request->get('type', 'all');

        if (!$repair_id) {
            $this->error('参数错误');
        }

        $query = \app\admin\model\RepairEvaluate::where('repair_id', $repair_id);

        // 根据评价类型筛选
        switch ($type) {
            case 'good':
                $query->where('star', '>=', 4);
                break;
            case 'bad':
                $query->where('star', '<=', 3);
                break;
            case 'all':
            default:
                // 不添加额外条件，显示全部
                break;
        }

        $list = $query->order('weigh', 'desc')
            ->paginate($limit, false, ['page' => $page])->each(function ($item) {
                // 限制返回字段
                $item->visible(['id', 'repair_id', 'star', 'star_text', 'content', 'images', 'nickname', 'avatar', 'createtime']);
                return $item;
            });

        $this->success('获取成功', $list);
    }

    /**
     * 获取配件列表
     *
     * @ApiMethod (GET)
     * @ApiParams (name="page", type="integer", required=false, description="页码")
     * @ApiParams (name="limit", type="integer", required=false, description="每页数量")
     * @ApiParams (name="brand_ids", type="string", required=false, description="品牌ID")
     * @ApiParams (name="range_ids", type="string", required=false, description="范围ID")
     * @ApiParams (name="lng", type="string", required=false, description="经度")
     * @ApiParams (name="lat", type="string", required=false, description="纬度")
     */
    public function getAccessoryList()
    {
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);
        $brand_ids = $this->request->get('brand_ids', '');
        $range_ids = $this->request->get('range_ids', '');
        $lng = $this->request->get('lng', '');
        $lat = $this->request->get('lat', '');

        $query = \app\admin\model\Accessory::with(['user', 'company'])
            ->where('accessory.status', 'paid')
            ->where('accessory.user_id', '>', 0)
            ->where('company.id', '>', 0);

        // 使用find_in_set查询brand_id
        if (!empty($brand_ids)) {
            $query->whereRaw("FIND_IN_SET(?, brand_ids)", [$brand_ids]);
        }

        // 使用find_in_set查询range_id
        if (!empty($range_ids)) {
            $query->whereRaw("FIND_IN_SET(?, range_ids)", [$range_ids]);
        }

        // 如果传入了经纬度，添加距离计算和排序
        if (!empty($lng) && !empty($lat)) {
            $query->field('accessory.*, (6371 * acos(cos(radians(' . $lat . ')) * cos(radians(accessory.lat)) * cos(radians(accessory.lng) - radians(' . $lng . ')) + sin(radians(' . $lat . ')) * sin(radians(accessory.lat)))) AS distance')
                ->order('distance', 'asc');
        } else {
            $query->order('createtime', 'desc');
        }

        $list = $query->paginate($limit, false, ['page' => $page])->each(function ($item) use ($lng, $lat) {
            if (empty($lng) || empty($lat)) {
                $item->setAttr('distance', 0);
            } else {
                $item->distance = round($item->distance, 2);
            }
            // 限制返回字段
            $item->visible(['id', 'image', 'images', 'images_arr', 'name', 'lng', 'lat', 'address', 'rate', 'rate_num', 'brand_ids', 'distance']);
            return $item;
        });

        $this->success('获取成功', $list);
    }

    /**
     * 获取配件详情
     *
     * @ApiMethod (GET)
     * @ApiParams (name="id", type="integer", required=true, description="配件ID")
     * @ApiParams (name="lng", type="string", required=false, description="经度")
     * @ApiParams (name="lat", type="string", required=false, description="纬度")
     */
    public function getAccessoryDetail()
    {
        $id = $this->request->get('id');
        $lng = $this->request->get('lng', '');
        $lat = $this->request->get('lat', '');

        if (!$id) {
            $this->error('参数错误');
        }

        $accessory = \app\admin\model\Accessory::with(['user', 'company'])
            ->where('accessory.id', $id)
            ->where('accessory.status', 'paid')
            ->where('accessory.user_id', '>', 0)
            ->where('company.id', '>', 0)
            ->find();

        if (!$accessory) {
            $this->error('数据不存在');
        }

        // 关联查询brand_id对应的品牌数据
        if (!empty($accessory->brand_ids)) {
            $brandIds = explode(',', $accessory->brand_ids);
            $brands = \app\admin\model\Brand::whereIn('id', $brandIds)->select();
            $accessory->brands = $brands;
        } else {
            $accessory->brands = [];
        }

        // 关联查询range_id对应的范围数据
        if (!empty($accessory->range_ids)) {
            $rangeIds = explode(',', $accessory->range_ids);
            $ranges = \app\admin\model\Range::whereIn('id', $rangeIds)->select();
            $accessory->ranges = $ranges;
        } else {
            $accessory->ranges = [];
        }

        // 如果传入了经纬度，计算距离
        if (!empty($lng) && !empty($lat) && !empty($accessory->lng) && !empty($accessory->lat)) {
            $accessory->distance = $this->calculateDistance($lat, $lng, $accessory->lat, $accessory->lng);
        } else {
            $accessory->distance = 0;
        }

        // 限制返回字段
        $accessory->visible(['id', 'image', 'images', 'images_arr', 'name', 'lng', 'lat', 'address', 'rate', 'rate_num', 'brand_id', 'distance', 'brands', 'ranges']);

        $this->success('获取成功', $accessory);
    }

    /**
     * 获取配件评价列表
     *
     * @ApiMethod (GET)
     * @ApiParams (name="accessory_id", type="integer", required=true, description="配件ID")
     * @ApiParams (name="page", type="integer", required=false, description="页码")
     * @ApiParams (name="limit", type="integer", required=false, description="每页数量")
     * @ApiParams (name="type", type="string", required=false, description="评价类型：all=全部，good=好评(4-5星)，bad=差评(1-3星)")
     */
    public function getAccessoryEvaluateList()
    {
        $accessory_id = $this->request->get('accessory_id');
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);
        $type = $this->request->get('type', 'all');

        if (!$accessory_id) {
            $this->error('参数错误');
        }

        $query = \app\admin\model\AccessoryEvaluate::where('accessory_id', $accessory_id);

        // 根据评价类型筛选
        switch ($type) {
            case 'good':
                $query->where('star', '>=', 4);
                break;
            case 'bad':
                $query->where('star', '<=', 3);
                break;
            case 'all':
            default:
                // 不添加额外条件，显示全部
                break;
        }

        $list = $query->order('weigh', 'desc')
            ->paginate($limit, false, ['page' => $page])->each(function ($item) {
                // 限制返回字段
                $item->visible(['id', 'accessory_id', 'star', 'star_text', 'content', 'images', 'nickname', 'avatar', 'createtime']);
                return $item;
            });

        $this->success('获取成功', $list);
    }

    /**
     * 获取车辆列表
     *
     * @ApiMethod (GET)
     * @ApiParams (name="page", type="integer", required=false, description="页码")
     * @ApiParams (name="limit", type="integer", required=false, description="每页数量")
     * @ApiParams (name="province", type="string", required=false, description="省份")
     * @ApiParams (name="city", type="string", required=false, description="城市")
     * @ApiParams (name="age", type="string", required=false, description="车龄区间，格式：最小值-最大值，如：2-5")
     * @ApiParams (name="price", type="string", required=false, description="价格区间，格式：最小值-最大值，如：10-15")
     * @ApiParams (name="car_type_id", type="integer", required=false, description="车型ID")
     * @ApiParams (name="lng", type="string", required=false, description="经度")
     * @ApiParams (name="lat", type="string", required=false, description="纬度")
     */
    public function getCarList()
    {
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);
        $province = $this->request->get('province', '');
        $city = $this->request->get('city', '');
        $age = $this->request->get('age', '');
        $price = $this->request->get('price', '');
        $lng = $this->request->get('lng', '');
        $lat = $this->request->get('lat', '');
        $car_type_id = $this->request->get('car_type_id', '');

        $query = \app\admin\model\Car::with(['user', 'type', 'company'])
            ->where('car.status', 'paid')
            ->where('car.user_id', '>', 0)
            ->where('company.id', '>', 0);

        // 查询province
        if (!empty($province)) {
            $query->where('province', $province);
        }

        // 查询city
        if (!empty($city)) {
            $query->where('city', $city);
        }

        // 区间查询age (格式: 18-45)
        if (!empty($age)) {
            if (strpos($age, '-') !== false) {
                $ageRange = explode('-', $age);
                if (count($ageRange) == 2) {
                    $query->whereBetween('age', $ageRange);
                }
            } else {
                // 如果不是区间格式，按精确匹配
                $query->where('age', $age);
            }
        }

        // 区间查询price (格式: 10-15)
        if (!empty($price)) {
            if (strpos($price, '-') !== false) {
                $priceRange = explode('-', $price);
                if (count($priceRange) == 2) {
                    $query->whereBetween('price', $priceRange);
                }
            } else {
                // 如果不是区间格式，按精确匹配
                $query->where('price', $price);
            }
        }

        //查询车型
        if (!empty($car_type_id)) {
            $query->where('car_type_id', $car_type_id);
        }

        // 如果传入了经纬度，添加距离计算和排序
        if (!empty($lng) && !empty($lat)) {
            $query->field('car.*, (6371 * acos(cos(radians(' . $lat . ')) * cos(radians(car.lat)) * cos(radians(car.lng) - radians(' . $lng . ')) + sin(radians(' . $lat . ')) * sin(radians(car.lat)))) AS distance')
                ->order('distance', 'asc');
        } else {
            $query->order('createtime', 'desc');
        }

        $list = $query->paginate($limit, false, ['page' => $page])->each(function ($item) use ($lng, $lat) {
            if (empty($lng) || empty($lat)) {
                $item->setAttr('distance', 0);
            } else {
                $item->distance = round($item->distance, 2);
            }
            // 限制返回字段
            $item->visible(['id', 'image', 'images', 'images_arr', 'name', 'lng', 'lat', 'address', 'rate', 'rate_num', 'car_type_id', 'distance', 'city', 'province', 'age', 'horsepower', 'type']);
            return $item;
        });

        $this->success('获取成功', $list);
    }

    /**
     * 获取车辆详情
     *
     * @ApiMethod (GET)
     * @ApiParams (name="id", type="integer", required=true, description="车辆ID")
     * @ApiParams (name="lng", type="string", required=false, description="经度")
     * @ApiParams (name="lat", type="string", required=false, description="纬度")
     */
    public function getCarDetail()
    {
        $id = $this->request->get('id');
        $lng = $this->request->get('lng', '');
        $lat = $this->request->get('lat', '');

        if (!$id) {
            $this->error('参数错误');
        }

        $car = \app\admin\model\Car::with(['user', 'type', 'company'])
            ->where('car.id', $id)
            ->where('car.status', 'paid')
            ->where('car.user_id', '>', 0)
            ->where('company.id', '>', 0)
            ->find();

        if (!$car) {
            $this->error('数据不存在');
        }

        // 如果传入了经纬度，计算距离
        if (!empty($lng) && !empty($lat) && !empty($car->lng) && !empty($car->lat)) {
            $car->distance = $this->calculateDistance($lat, $lng, $car->lat, $car->lng);
        } else {
            $car->distance = 0;
        }

        // 限制返回字段
        $car->visible(['id', 'image', 'images', 'images_arr', 'name', 'lng', 'lat', 'address', 'rate', 'rate_num', 'car_type_id', 'distance', 'city', 'province', 'age', 'horsepower', 'type']);
        $this->success('获取成功', $car);
    }

    /**
     * 获取服务列表
     *
     * @ApiMethod (GET)
     * @ApiParams (name="page", type="integer", required=false, description="页码")
     * @ApiParams (name="limit", type="integer", required=false, description="每页数量")
     * @ApiParams (name="lng", type="string", required=false, description="经度")
     * @ApiParams (name="lat", type="string", required=false, description="纬度")
     */
    public function getServerList()
    {
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);
        $lng = $this->request->get('lng', '');
        $lat = $this->request->get('lat', '');

        $query = \app\admin\model\Server::with(['user', 'company'])
            ->where('server.status', 'paid')
            ->where('server.user_id', '>', 0)
            ->where('company.id', '>', 0);

        // 如果传入了经纬度，添加距离计算和排序
        if (!empty($lng) && !empty($lat)) {
            $query->field('server.*, (6371 * acos(cos(radians(' . $lat . ')) * cos(radians(server.lat)) * cos(radians(server.lng) - radians(' . $lng . ')) + sin(radians(' . $lat . ')) * sin(radians(server.lat)))) AS distance')
                ->order('distance', 'asc');
        } else {
            $query->order('createtime', 'desc');
        }

        $list = $query->paginate($limit, false, ['page' => $page])->each(function ($item) use ($lng, $lat) {
            if (empty($lng) || empty($lat)) {
                $item->setAttr('distance', 0);
            } else {
                $item->distance = round($item->distance, 2);
            }
            // 限制返回字段
            $item->visible(['id', 'image', 'images', 'images_arr', 'name', 'lng', 'lat', 'address', 'rate', 'rate_num', 'distance']);
            return $item;
        });

        $this->success('获取成功', $list);
    }

    /**
     * 获取服务详情
     *
     * @ApiMethod (GET)
     * @ApiParams (name="id", type="integer", required=true, description="服务ID")
     * @ApiParams (name="lng", type="string", required=false, description="经度")
     * @ApiParams (name="lat", type="string", required=false, description="纬度")
     */
    public function getServerDetail()
    {
        $id = $this->request->get('id');
        $lng = $this->request->get('lng', '');
        $lat = $this->request->get('lat', '');

        if (!$id) {
            $this->error('参数错误');
        }

        $server = \app\admin\model\Server::with(['user', 'company'])
            ->where('server.id', $id)
            ->where('server.status', 'paid')
            ->where('server.user_id', '>', 0)
            ->where('company.id', '>', 0)
            ->find();

        if (!$server) {
            $this->error('数据不存在');
        }

        // user_id和company_id已经通过with关联了

        // 如果传入了经纬度，计算距离
        if (!empty($lng) && !empty($lat) && !empty($server->lng) && !empty($server->lat)) {
            $server->distance = $this->calculateDistance($lat, $lng, $server->lat, $server->lng);
        } else {
            $server->distance = 0;
        }

        // 限制返回字段
        $server->visible(['id', 'image', 'images', 'images_arr', 'name', 'lng', 'lat', 'address', 'rate', 'rate_num', 'distance']);

        $this->success('获取成功', $server);
    }

    /**
     * 获取服务评价列表
     *
     * @ApiMethod (GET)
     * @ApiParams (name="server_id", type="integer", required=true, description="服务ID")
     * @ApiParams (name="page", type="integer", required=false, description="页码")
     * @ApiParams (name="limit", type="integer", required=false, description="每页数量")
     * @ApiParams (name="type", type="string", required=false, description="评价类型：all=全部，good=好评(4-5星)，bad=差评(1-3星)")
     */
    public function getServerEvaluateList()
    {
        $server_id = $this->request->get('server_id');
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);
        $type = $this->request->get('type', 'all');

        if (!$server_id) {
            $this->error('参数错误');
        }

        $query = \app\admin\model\ServerEvaluate::where('server_id', $server_id);

        // 根据评价类型筛选
        switch ($type) {
            case 'good':
                $query->where('star', '>=', 4);
                break;
            case 'bad':
                $query->where('star', '<=', 3);
                break;
            case 'all':
            default:
                // 不添加额外条件，显示全部
                break;
        }

        $list = $query->order('weigh', 'desc')
            ->paginate($limit, false, ['page' => $page])->each(function ($item) {
                // 限制返回字段
                $item->visible(['id', 'server_id', 'star', 'star_text', 'content', 'images', 'nickname', 'avatar', 'createtime']);
                return $item;
            });

        $this->success('获取成功', $list);
    }

    /**
     * 获取检测列表
     *
     * @ApiMethod (GET)
     * @ApiParams (name="page", type="integer", required=false, description="页码")
     * @ApiParams (name="limit", type="integer", required=false, description="每页数量")
     * @ApiParams (name="lng", type="string", required=false, description="经度")
     * @ApiParams (name="lat", type="string", required=false, description="纬度")
     */
    public function getInspectionList()
    {
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);
        $lng = $this->request->get('lng', '');
        $lat = $this->request->get('lat', '');

        $query = \app\admin\model\Inspection::with(['user', 'company'])
            ->where('inspection.status', 'paid')
            ->where('inspection.user_id', '>', 0)
            ->where('company.id', '>', 0);

        // 如果传入了经纬度，添加距离计算和排序
        if (!empty($lng) && !empty($lat)) {
            $query->field('inspection.*, (6371 * acos(cos(radians(' . $lat . ')) * cos(radians(inspection.lat)) * cos(radians(inspection.lng) - radians(' . $lng . ')) + sin(radians(' . $lat . ')) * sin(radians(inspection.lat)))) AS distance')
                ->order('distance', 'asc');
        } else {
            $query->order('createtime', 'desc');
        }

        $list = $query->paginate($limit, false, ['page' => $page])->each(function ($item) use ($lng, $lat) {
            if (empty($lng) || empty($lat)) {
                $item->setAttr('distance', 0);
            } else {
                $item->distance = round($item->distance, 2);
            }
            // 限制返回字段
            $item->visible(['id', 'image', 'images', 'images_arr', 'name', 'lng', 'lat', 'address', 'rate', 'rate_num', 'distance']);
            return $item;
        });

        $this->success('获取成功', $list);
    }

    /**
     * 获取检测详情
     *
     * @ApiMethod (GET)
     * @ApiParams (name="id", type="integer", required=true, description="检测ID")
     * @ApiParams (name="lng", type="string", required=false, description="经度")
     * @ApiParams (name="lat", type="string", required=false, description="纬度")
     */
    public function getInspectionDetail()
    {
        $id = $this->request->get('id');
        $lng = $this->request->get('lng', '');
        $lat = $this->request->get('lat', '');

        if (!$id) {
            $this->error('参数错误');
        }

        $inspection = \app\admin\model\Inspection::with(['user', 'company'])
            ->where('inspection.id', $id)
            ->where('inspection.status', 'paid')
            ->where('inspection.user_id', '>', 0)
            ->where('company.id', '>', 0)
            ->find();

        if (!$inspection) {
            $this->error('数据不存在');
        }

        // user_id和company_id已经通过with关联了

        // 如果传入了经纬度，计算距离
        if (!empty($lng) && !empty($lat) && !empty($inspection->lng) && !empty($inspection->lat)) {
            $inspection->distance = $this->calculateDistance($lat, $lng, $inspection->lat, $inspection->lng);
        } else {
            $inspection->distance = 0;
        }

        // 限制返回字段
        $inspection->visible(['id', 'image', 'images', 'images_arr', 'name', 'lng', 'lat', 'address', 'rate', 'rate_num', 'distance']);

        $this->success('获取成功', $inspection);
    }

    /**
     * 获取检测评价列表
     *
     * @ApiMethod (GET)
     * @ApiParams (name="inspection_id", type="integer", required=true, description="检测ID")
     * @ApiParams (name="page", type="integer", required=false, description="页码")
     * @ApiParams (name="limit", type="integer", required=false, description="每页数量")
     * @ApiParams (name="type", type="string", required=false, description="评价类型：all=全部，good=好评(4-5星)，bad=差评(1-3星)")
     */
    public function getInspectionEvaluateList()
    {
        $inspection_id = $this->request->get('inspection_id');
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);
        $type = $this->request->get('type', 'all');

        if (!$inspection_id) {
            $this->error('参数错误');
        }

        $query = \app\admin\model\InspectionEvaluate::where('inspection_id', $inspection_id);

        // 根据评价类型筛选
        switch ($type) {
            case 'good':
                $query->where('star', '>=', 4);
                break;
            case 'bad':
                $query->where('star', '<=', 3);
                break;
            case 'all':
            default:
                // 不添加额外条件，显示全部
                break;
        }

        $list = $query->order('weigh', 'desc')
            ->paginate($limit, false, ['page' => $page])->each(function ($item) {
                // 限制返回字段
                $item->visible(['id', 'inspection_id', 'star', 'star_text', 'content', 'images', 'nickname', 'avatar', 'createtime']);
                return $item;
            });

        $this->success('获取成功', $list);
    }

    /**
     * 获取吊车列表
     *
     * @ApiMethod (GET)
     * @ApiParams (name="page", type="integer", required=false, description="页码")
     * @ApiParams (name="limit", type="integer", required=false, description="每页数量")
     * @ApiParams (name="lng", type="string", required=false, description="经度")
     * @ApiParams (name="lat", type="string", required=false, description="纬度")
     */
    public function getCraneList()
    {
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);
        $lng = $this->request->get('lng', '');
        $lat = $this->request->get('lat', '');

        $query = \app\admin\model\Crane::with(['user', 'company'])
            ->where('crane.status', 'paid')
            ->where('crane.user_id', '>', 0)
            ->where('company.id', '>', 0);

        // 如果传入了经纬度，添加距离计算和排序
        if (!empty($lng) && !empty($lat)) {
            $query->field('crane.*, (6371 * acos(cos(radians(' . $lat . ')) * cos(radians(crane.lat)) * cos(radians(crane.lng) - radians(' . $lng . ')) + sin(radians(' . $lat . ')) * sin(radians(crane.lat)))) AS distance')
                ->order('distance', 'asc');
        } else {
            $query->order('createtime', 'desc');
        }

        $list = $query->paginate($limit, false, ['page' => $page])->each(function ($item) use ($lng, $lat) {
            if (empty($lng) || empty($lat)) {
                $item->setAttr('distance', 0);
            } else {
                $item->distance = round($item->distance, 2);
            }
            // 限制返回字段
            $item->visible(['id', 'image', 'images', 'images_arr', 'name', 'lng', 'lat', 'address', 'rate', 'rate_num', 'distance']);
            return $item;
        });

        $this->success('获取成功', $list);
    }

    /**
     * 获取吊车详情
     *
     * @ApiMethod (GET)
     * @ApiParams (name="id", type="integer", required=true, description="吊车ID")
     * @ApiParams (name="lng", type="string", required=false, description="经度")
     * @ApiParams (name="lat", type="string", required=false, description="纬度")
     */
    public function getCraneDetail()
    {
        $id = $this->request->get('id');
        $lng = $this->request->get('lng', '');
        $lat = $this->request->get('lat', '');

        if (!$id) {
            $this->error('参数错误');
        }

        $crane = \app\admin\model\Crane::with(['user', 'company'])
            ->where('crane.id', $id)
            ->where('crane.status', 'paid')
            ->where('crane.user_id', '>', 0)
            ->where('company.id', '>', 0)
            ->find();

        if (!$crane) {
            $this->error('数据不存在');
        }

        // user_id和company_id已经通过with关联了

        // 如果传入了经纬度，计算距离
        if (!empty($lng) && !empty($lat) && !empty($crane->lng) && !empty($crane->lat)) {
            $crane->distance = $this->calculateDistance($lat, $lng, $crane->lat, $crane->lng);
        } else {
            $crane->distance = 0;
        }

        // 限制返回字段
        $crane->visible(['id', 'image', 'images', 'images_arr', 'name', 'lng', 'lat', 'address', 'rate', 'rate_num', 'distance']);

        $this->success('获取成功', $crane);
    }

    /**
     * 获取吊车评价列表
     *
     * @ApiMethod (GET)
     * @ApiParams (name="crane_id", type="integer", required=true, description="吊车ID")
     * @ApiParams (name="page", type="integer", required=false, description="页码")
     * @ApiParams (name="limit", type="integer", required=false, description="每页数量")
     * @ApiParams (name="type", type="string", required=false, description="评价类型：all=全部，good=好评(4-5星)，bad=差评(1-3星)")
     */
    public function getCraneEvaluateList()
    {
        $crane_id = $this->request->get('crane_id');
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);
        $type = $this->request->get('type', 'all');

        if (!$crane_id) {
            $this->error('参数错误');
        }

        $query = \app\admin\model\CraneEvaluate::where('crane_id', $crane_id);

        // 根据评价类型筛选
        switch ($type) {
            case 'good':
                $query->where('star', '>=', 4);
                break;
            case 'bad':
                $query->where('star', '<=', 3);
                break;
            case 'all':
            default:
                // 不添加额外条件，显示全部
                break;
        }

        $list = $query->order('weigh', 'desc')
            ->paginate($limit, false, ['page' => $page])->each(function ($item) {
                // 限制返回字段
                $item->visible(['id', 'crane_id', 'star', 'star_text', 'content', 'images', 'nickname', 'avatar', 'createtime']);
                return $item;
            });

        $this->success('获取成功', $list);
    }

    /**
     * 获取篷布列表
     *
     * @ApiMethod (GET)
     * @ApiParams (name="page", type="integer", required=false, description="页码")
     * @ApiParams (name="limit", type="integer", required=false, description="每页数量")
     * @ApiParams (name="lng", type="string", required=false, description="经度")
     * @ApiParams (name="lat", type="string", required=false, description="纬度")
     */
    public function getTarpaulinList()
    {
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);
        $lng = $this->request->get('lng', '');
        $lat = $this->request->get('lat', '');

        $query = \app\admin\model\Tarpaulin::with(['user', 'company'])
            ->where('tarpaulin.status', 'paid')
            ->where('tarpaulin.user_id', '>', 0)
            ->where('company.id', '>', 0);

        // 如果传入了经纬度，添加距离计算和排序
        if (!empty($lng) && !empty($lat)) {
            $query->field('tarpaulin.*, (6371 * acos(cos(radians(' . $lat . ')) * cos(radians(tarpaulin.lat)) * cos(radians(tarpaulin.lng) - radians(' . $lng . ')) + sin(radians(' . $lat . ')) * sin(radians(tarpaulin.lat)))) AS distance')
                ->order('distance', 'asc');
        } else {
            $query->order('createtime', 'desc');
        }

        $list = $query->paginate($limit, false, ['page' => $page])->each(function ($item) use ($lng, $lat) {
            if (empty($lng) || empty($lat)) {
                $item->setAttr('distance', 0);
            } else {
                $item->distance = round($item->distance, 2);
            }
            // 限制返回字段
            $item->visible(['id', 'image', 'images', 'images_arr', 'name', 'lng', 'lat', 'address', 'rate', 'rate_num', 'distance']);
            return $item;
        });

        $this->success('获取成功', $list);
    }

    /**
     * 获取篷布详情
     *
     * @ApiMethod (GET)
     * @ApiParams (name="id", type="integer", required=true, description="篷布ID")
     * @ApiParams (name="lng", type="string", required=false, description="经度")
     * @ApiParams (name="lat", type="string", required=false, description="纬度")
     */
    public function getTarpaulinDetail()
    {
        $id = $this->request->get('id');
        $lng = $this->request->get('lng', '');
        $lat = $this->request->get('lat', '');

        if (!$id) {
            $this->error('参数错误');
        }

        $tarpaulin = \app\admin\model\Tarpaulin::with(['user', 'company'])
            ->where('tarpaulin.id', $id)
            ->where('tarpaulin.status', 'paid')
            ->where('tarpaulin.user_id', '>', 0)
            ->where('company.id', '>', 0)
            ->find();

        if (!$tarpaulin) {
            $this->error('数据不存在');
        }

        // 关联查询repair_car_ids对应的车辆类型数据
        if (!empty($tarpaulin->repair_car_ids)) {
            $carIds = explode(',', $tarpaulin->repair_car_ids);
            $cars = \app\admin\model\RepairCar::whereIn('id', $carIds)->select();
            $tarpaulin->repair_cars = $cars;
        } else {
            $tarpaulin->repair_cars = [];
        }

        // 关联查询repair_range_ids对应的维修范围数据
        if (!empty($tarpaulin->repair_range_ids)) {
            $rangeIds = explode(',', $tarpaulin->repair_range_ids);
            $ranges = \app\admin\model\RepairRange::whereIn('id', $rangeIds)->select();
            $tarpaulin->repair_ranges = $ranges;
        } else {
            $tarpaulin->repair_ranges = [];
        }

        // 如果传入了经纬度，计算距离
        if (!empty($lng) && !empty($lat) && !empty($tarpaulin->lng) && !empty($tarpaulin->lat)) {
            $tarpaulin->distance = $this->calculateDistance($lat, $lng, $tarpaulin->lat, $tarpaulin->lng);
        } else {
            $tarpaulin->distance = 0;
        }

        // 限制返回字段
        $tarpaulin->visible(['id', 'image', 'images', 'images_arr', 'name', 'lng', 'lat', 'address', 'rate', 'rate_num', 'distance', 'repair_cars', 'repair_ranges']);

        $this->success('获取成功', $tarpaulin);
    }

    /**
     * 获取篷布评价列表
     *
     * @ApiMethod (GET)
     * @ApiParams (name="tarpaulin_id", type="integer", required=true, description="篷布ID")
     * @ApiParams (name="page", type="integer", required=false, description="页码")
     * @ApiParams (name="limit", type="integer", required=false, description="每页数量")
     * @ApiParams (name="type", type="string", required=false, description="评价类型：all=全部，good=好评(4-5星)，bad=差评(1-3星)")
     */
    public function getTarpaulinEvaluateList()
    {
        $tarpaulin_id = $this->request->get('tarpaulin_id');
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);
        $type = $this->request->get('type', 'all');

        if (!$tarpaulin_id) {
            $this->error('参数错误');
        }

        $query = \app\admin\model\TarpaulinEvaluate::where('tarpaulin_id', $tarpaulin_id);

        // 根据评价类型筛选
        switch ($type) {
            case 'good':
                $query->where('star', '>=', 4);
                break;
            case 'bad':
                $query->where('star', '<=', 3);
                break;
            case 'all':
            default:
                // 不添加额外条件，显示全部
                break;
        }

        $list = $query->order('weigh', 'desc')
            ->paginate($limit, false, ['page' => $page])->each(function ($item) {
                // 限制返回字段
                $item->visible(['id', 'tarpaulin_id', 'star', 'star_text', 'content', 'images', 'nickname', 'avatar', 'createtime']);
                return $item;
            });

        $this->success('获取成功', $list);
    }



    /**
     * 计算两点间距离（单位：公里）
     * 使用Haversine公式
     */
    private function calculateDistance($lat1, $lng1, $lat2, $lng2)
    {
        $earthRadius = 6371; // 地球半径，单位：公里

        $lat1 = deg2rad($lat1);
        $lng1 = deg2rad($lng1);
        $lat2 = deg2rad($lat2);
        $lng2 = deg2rad($lng2);

        $deltaLat = $lat2 - $lat1;
        $deltaLng = $lng2 - $lng1;

        $a = sin($deltaLat / 2) * sin($deltaLat / 2) +
            cos($lat1) * cos($lat2) *
            sin($deltaLng / 2) * sin($deltaLng / 2);
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        $distance = $earthRadius * $c;

        return round($distance, 2); // 保留两位小数
    }
}
