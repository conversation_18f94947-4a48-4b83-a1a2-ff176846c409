<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-rule="required" data-source="user/user/index" disabled data-field="nickname"
                class="form-control selectpage" name="row[user_id]" type="text" value="{$row.user_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('企业类别')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-company_category_id" data-rule="required" data-source="company_category/index" disabled
                class="form-control selectpage" name="row[company_category_id]" type="text"
                value="{$row.company_category_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('License_image')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-license_image" class="form-control" size="50" name="row[license_image]" type="text"
                    value="{$row.license_image|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-license_image" class="btn btn-danger faupload"
                            data-input-id="c-license_image"
                            data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp"
                            data-multiple="false" data-preview-id="p-license_image"><i class="fa fa-upload"></i>
                            {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-license_image" class="btn btn-primary fachoose"
                            data-input-id="c-license_image" data-mimetype="image/*" data-multiple="false"><i
                                class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-license_image"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-license_image"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" class="form-control" name="row[name]" type="text" value="{$row.name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Code')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-code" class="form-control" name="row[code]" type="text" value="{$row.code|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Front_image')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-front_image" class="form-control" size="50" name="row[front_image]" type="text"
                    value="{$row.front_image|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-front_image" class="btn btn-danger faupload"
                            data-input-id="c-front_image"
                            data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp"
                            data-multiple="false" data-preview-id="p-front_image"><i class="fa fa-upload"></i>
                            {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-front_image" class="btn btn-primary fachoose"
                            data-input-id="c-front_image" data-mimetype="image/*" data-multiple="false"><i
                                class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-front_image"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-front_image"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Reverse_image')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-reverse_image" class="form-control" size="50" name="row[reverse_image]" type="text"
                    value="{$row.reverse_image|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-reverse_image" class="btn btn-danger faupload"
                            data-input-id="c-reverse_image"
                            data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp"
                            data-multiple="false" data-preview-id="p-reverse_image"><i class="fa fa-upload"></i>
                            {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-reverse_image" class="btn btn-primary fachoose"
                            data-input-id="c-reverse_image" data-mimetype="image/*" data-multiple="false"><i
                                class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-reverse_image"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-reverse_image"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Legal_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-legal_name" class="form-control" name="row[legal_name]" type="text"
                value="{$row.legal_name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Legal_card')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-legal_card" class="form-control" name="row[legal_card]" type="text"
                value="{$row.legal_card|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">

            <div class="radio">
                {foreach name="statusList" item="vo"}
                <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio"
                        value="{$key}" {in name="key" value="$row.status" }checked{/in} /> {$vo}</label>
                {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group" data-favisible="status=refuse">
        <label class="control-label col-xs-12 col-sm-2">{:__('拒绝理由')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-refuse_remark" class="form-control" name="row[refuse_remark]" type="text"
                value="{$row.refuse_remark|htmlentities}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>