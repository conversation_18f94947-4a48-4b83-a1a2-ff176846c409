<style type="text/css">
    /* 现代化控制台样式 */
    .dashboard-container {
        background: #f8f9fa;
        padding: 20px 0;
    }

    /* 统计卡片样式 */
    .modern-stat-card {
        background: #fff;
        border-radius: 16px;
        padding: 24px;
        margin-bottom: 20px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        border: 1px solid #e9ecef;
        position: relative;
        overflow: hidden;
    }

    .modern-stat-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    }

    .modern-stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
    }

    .modern-stat-card.order-card::before {
        background: linear-gradient(90deg, #4a90e2 0%, #5ba3f5 100%);
    }

    .modern-stat-card.vip-card::before {
        background: linear-gradient(90deg, #e74c3c 0%, #f39c12 100%);
    }

    .modern-stat-card.goods-card::before {
        background: linear-gradient(90deg, #2ecc71 0%, #27ae60 100%);
    }

    .modern-stat-card.user-card::before {
        background: linear-gradient(90deg, #9b59b6 0%, #8e44ad 100%);
    }

    .stat-icon-modern {
        width: 64px;
        height: 64px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28px;
        color: #fff;
        margin-bottom: 16px;
        float: left;
        margin-right: 16px;
    }

    .modern-stat-card.order-card .stat-icon-modern {
        background: linear-gradient(135deg, #4a90e2 0%, #5ba3f5 100%);
    }

    .modern-stat-card.vip-card .stat-icon-modern {
        background: linear-gradient(135deg, #e74c3c 0%, #f39c12 100%);
    }

    .modern-stat-card.goods-card .stat-icon-modern {
        background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
    }

    .modern-stat-card.user-card .stat-icon-modern {
        background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
    }

    .stat-content-modern {
        overflow: hidden;
        padding-top: 8px;
    }

    .stat-number-modern {
        font-size: 36px;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 4px;
        line-height: 1;
    }

    .stat-label-modern {
        font-size: 16px;
        color: #6c757d;
        font-weight: 500;
        margin-bottom: 8px;
    }

    .stat-trend-modern {
        font-size: 12px;
        color: #27ae60;
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .stat-trend-modern.down {
        color: #e74c3c;
    }

    /* 图表区域保持原有样式但优化 */
    .chart-container-modern {
        background: #fff;
        border-radius: 16px;
        padding: 24px;
        margin-bottom: 20px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        border: 1px solid #e9ecef;
    }

    .chart-header-modern {
        margin-bottom: 20px;
        padding-bottom: 16px;
        border-bottom: 2px solid #e9ecef;
    }

    .chart-title-modern {
        font-size: 20px;
        font-weight: 600;
        color: #2c3e50;
        margin: 0;
    }

    /* 审核面板样式 */
    .audit-panel-modern {
        background: #fff;
        border-radius: 16px;
        padding: 24px;
        margin-bottom: 20px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        border: 1px solid #e9ecef;
        position: relative;
        cursor: pointer;
        transition: all 0.3s ease;
        border-left: 4px solid #ffc107;
    }

    .audit-panel-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    }

    .audit-panel-modern.urgent {
        border-left-color: #dc3545;
    }

    .audit-panel-modern.normal {
        border-left-color: #28a745;
    }

    .audit-header-modern {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
    }

    .audit-title-modern {
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
        margin: 0;
    }

    .audit-badge-modern {
        background: #ffc107;
        color: #fff;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
    }

    .audit-badge-modern.urgent {
        background: #dc3545;
    }

    .audit-badge-modern.normal {
        background: #28a745;
    }

    .audit-count-modern {
        font-size: 32px;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 8px;
    }

    .audit-desc-modern {
        font-size: 14px;
        color: #6c757d;
        margin-bottom: 16px;
    }

    .audit-action-modern {
        display: flex;
        gap: 8px;
    }

    .btn-audit-modern {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #fff;
        border: none;
        padding: 8px 16px;
        border-radius: 8px;
        font-size: 13px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .btn-audit-modern:hover {
        opacity: 0.9;
        transform: translateY(-1px);
        color: #fff;
    }

    .btn-audit-modern.btn-warning {
        background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
    }

    .btn-audit-modern.btn-danger {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    }

    .btn-audit-modern.btn-success {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    }

    /* 保持原有的统计图表样式 */
    .stats .stat-icon {
        color: #28bb9c;
        display: inline-block;
        font-size: 26px;
        text-align: center;
        vertical-align: middle;
        width: 50px;
        float: left;
    }

    .stat {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: inline-block;
    }

    .stat .value {
        font-size: 20px;
        line-height: 24px;
        overflow: hidden;
        text-overflow: ellipsis;
        font-weight: 500;
    }

    .stat .name {
        overflow: hidden;
        text-overflow: ellipsis;
        margin: 5px 0;
    }

    .stat-col {
        margin: 0 0 10px 0;
    }

    .stat-col .progress {
        height: 2px;
    }

    .stat-col .progress-bar {
        line-height: 2px;
        height: 2px;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .modern-stat-card {
            padding: 16px;
        }

        .stat-number-modern {
            font-size: 28px;
        }

        .chart-container-modern {
            padding: 16px;
        }
    }
</style>
<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <ul class="nav nav-tabs">
            <li class="active"><a href="#one" data-toggle="tab">{:__('Dashboard')}</a></li>
        </ul>
    </div>
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">

                <!-- 现代化统计卡片 -->
                <div class="row">
                    <div class="col-md-3 col-sm-6 col-xs-12">
                        <div class="modern-stat-card order-card">
                            <div class="stat-icon-modern">
                                <i class="fa fa-shopping-cart"></i>
                            </div>
                            <div class="stat-content-modern">
                                <div class="stat-number-modern">{$totalorder|htmlentities}</div>
                                <div class="stat-label-modern">普通订单</div>
                                <div class="stat-trend-modern">
                                    <i class="fa fa-arrow-up"></i> 今日新增 {$todayorder|htmlentities}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6 col-xs-12">
                        <div class="modern-stat-card vip-card">
                            <div class="stat-icon-modern">
                                <i class="fa fa-diamond"></i>
                            </div>
                            <div class="stat-content-modern">
                                <div class="stat-number-modern">{$totalviporder|htmlentities}</div>
                                <div class="stat-label-modern">VIP订单</div>
                                <div class="stat-trend-modern">
                                    <i class="fa fa-arrow-up"></i> 今日新增 {$todayviporder|htmlentities}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6 col-xs-12">
                        <div class="modern-stat-card goods-card">
                            <div class="stat-icon-modern">
                                <i class="fa fa-truck"></i>
                            </div>
                            <div class="stat-content-modern">
                                <div class="stat-number-modern">{$totalgoods|htmlentities}</div>
                                <div class="stat-label-modern">货源信息</div>
                                <div class="stat-trend-modern">
                                    <i class="fa fa-arrow-up"></i> 今日新增 {$todaygoods|htmlentities}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6 col-xs-12">
                        <div class="modern-stat-card user-card">
                            <div class="stat-icon-modern">
                                <i class="fa fa-users"></i>
                            </div>
                            <div class="stat-content-modern">
                                <div class="stat-number-modern">{$totaluser|htmlentities}</div>
                                <div class="stat-label-modern">用户总数</div>
                                <div class="stat-trend-modern">
                                    <i class="fa fa-arrow-up"></i> 今日新增 {$todayusersignup|htmlentities}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 图表和统计区域 -->
                <div class="row">
                    <div class="col-lg-8">
                        <div class="chart-container-modern">
                            <div class="chart-header-modern">
                                <h3 class="chart-title-modern">数据趋势图表</h3>
                                <p class="chart-subtitle">实时业务数据统计</p>
                            </div>
                            <div id="echart" class="btn-refresh" style="height:300px;width:100%;"></div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="chart-container-modern">
                            <div class="chart-header-modern">
                                <h3 class="chart-title-modern">今日数据</h3>
                            </div>
                            <div class="row row-sm stats-container">
                                <div class="col-xs-6 stat-col">
                                    <div class="stat-icon"><i class="fa fa-clock-o"></i></div>
                                    <div class="stat">
                                        <div class="value"> {$todayorder|htmlentities}</div>
                                        <div class="name"> 今日新增订单</div>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar progress-bar-success" style="width: 20%"></div>
                                    </div>
                                </div>
                                <div class="col-xs-6 stat-col">
                                    <div class="stat-icon"><i class="fa fa-diamond"></i></div>
                                    <div class="stat">
                                        <div class="value"> {$todayviporder|htmlentities}</div>
                                        <div class="name"> 今日VIP订单</div>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar progress-bar-warning" style="width: 20%"></div>
                                    </div>
                                </div>
                                <div class="col-xs-6  stat-col">
                                    <div class="stat-icon"><i class="fa fa-truck"></i></div>
                                    <div class="stat">
                                        <div class="value"> {$todaygoods|htmlentities}</div>
                                        <div class="name"> 今日新增货源</div>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar progress-bar-info" style="width: 20%"></div>
                                    </div>
                                </div>
                                <div class="col-xs-6 stat-col">
                                    <div class="stat-icon"><i class="fa fa-user-plus"></i></div>
                                    <div class="stat">
                                        <div class="value"> {$todayusersignup|htmlentities}</div>
                                        <div class="name"> 今日新增用户</div>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar progress-bar-success" style="width: 20%"></div>
                                    </div>
                                </div>
                                <div class="col-xs-6  stat-col">
                                    <div class="stat-icon"><i class="fa fa-money"></i></div>
                                    <div class="stat">
                                        <div class="value"> ¥{$todayincome|htmlentities}</div>
                                        <div class="name"> 今日收入</div>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar progress-bar-danger" style="width: 20%"></div>
                                    </div>
                                </div>
                                <div class="col-xs-6  stat-col">
                                    <div class="stat-icon"><i class="fa fa-line-chart"></i></div>
                                    <div class="stat">
                                        <div class="value"> ¥{$totalincome|htmlentities}</div>
                                        <div class="name"> 总收入</div>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar progress-bar-primary" style="width: 20%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 审核统计区域 -->
                <div class="row" style="margin-top:20px;">
                    <div class="col-lg-12">
                        <div class="chart-container-modern">
                            <div class="chart-header-modern">
                                <h3 class="chart-title-modern"><i class="fa fa-check-circle"></i> 待审核数据统计</h3>
                                <p class="chart-subtitle">点击卡片可在弹窗中查看详细信息</p>
                            </div>

                            <div class="row">
                                <!-- 企业认证审核 -->
                                <div class="col-md-3 col-sm-6 col-xs-12">
                                    <div class="item audit-panel-soft" style="height: 120px;">
                                        <a href="{:url('company/index')}?status=pending" class="btn-dialog"
                                           title="企业认证审核" data-area='["80%","90%"]' icon="fa fa-building fa-fw">
                                            <div class="inner">
                                                <h1 style="font-size: 36px; color: #4a90e2;">{$pendingcompany|htmlentities}</h1>
                                            </div>
                                            <div class="txt" style="font-size: 14px; color: #5a6c7d;">企业认证审核</div>
                                        </a>
                                    </div>
                                </div>

                                <!-- 招聘企业审核 -->
                                <div class="col-md-3 col-sm-6 col-xs-12">
                                    <div class="item audit-panel-soft" style="height: 120px;">
                                        <a href="{:url('hire_company/index')}?status=pending" class="btn-dialog"
                                           title="招聘企业审核" data-area='["80%","90%"]' icon="fa fa-briefcase fa-fw">
                                            <div class="inner">
                                                <h1 style="font-size: 36px; color: #e74c3c;">{$pendinghirecompany|htmlentities}</h1>
                                            </div>
                                            <div class="txt" style="font-size: 14px; color: #5a6c7d;">招聘企业审核</div>
                                        </a>
                                    </div>
                                </div>

                                <!-- 货源审核 -->
                                <div class="col-md-3 col-sm-6 col-xs-12">
                                    <div class="item audit-panel-soft" style="height: 120px;">
                                        <a href="{:url('goods/index')}?status=audit" class="btn-dialog"
                                           title="货源信息审核" data-area='["80%","90%"]' icon="fa fa-truck fa-fw">
                                            <div class="inner">
                                                <h1 style="font-size: 36px; color: #2ecc71;">{$pendinggoods|htmlentities}</h1>
                                            </div>
                                            <div class="txt" style="font-size: 14px; color: #5a6c7d;">货源信息审核</div>
                                        </a>
                                    </div>
                                </div>

                                <!-- 司机认证审核 -->
                                <div class="col-md-3 col-sm-6 col-xs-12">
                                    <div class="item audit-panel-soft" style="height: 120px;">
                                        <a href="{:url('driver/index')}" class="btn-dialog"
                                           title="司机认证审核" data-area='["80%","90%"]' icon="fa fa-id-card fa-fw">
                                            <div class="inner">
                                                <h1 style="font-size: 36px; color: #9b59b6;">{$pendingdriver|htmlentities}</h1>
                                            </div>
                                            <div class="txt" style="font-size: 14px; color: #5a6c7d;">司机认证审核</div>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快速操作区域 -->
                <div class="row" style="margin-top:20px;">
                    <div class="col-lg-12">
                        <div class="chart-container-modern">
                            <div class="chart-header-modern">
                                <h3 class="chart-title-modern"><i class="fa fa-flash"></i> 快速操作</h3>
                                <p class="chart-subtitle">点击卡片可在弹窗中进行快速操作</p>
                            </div>

                            <div class="row">
                                <!-- 江湖动态审核 -->
                                <div class="col-md-3 col-sm-6 col-xs-12">
                                    <div class="item audit-panel-soft" style="height: 120px;">
                                        <a href="{:url('jianghu/index')}?status=pending" class="btn-dialog"
                                           title="江湖动态审核" data-area='["80%","90%"]' icon="fa fa-comments fa-fw">
                                            <div class="inner">
                                                <h1 style="font-size: 36px; color: #17a2b8;">{$pendingjianghu|htmlentities}</h1>
                                            </div>
                                            <div class="txt" style="font-size: 14px; color: #5a6c7d;">江湖动态审核</div>
                                        </a>
                                    </div>
                                </div>

                                <!-- 货源审核 -->
                                <div class="col-md-3 col-sm-6 col-xs-12">
                                    <div class="item audit-panel-soft" style="height: 120px;">
                                        <a href="{:url('goods_audit/index')}?status=pending" class="btn-dialog"
                                           title="货源审核" data-area='["80%","90%"]' icon="fa fa-check-square fa-fw">
                                            <div class="inner">
                                                <h1 style="font-size: 36px; color: #fd7e14;">{$pendinggoodsaudit|htmlentities}</h1>
                                            </div>
                                            <div class="txt" style="font-size: 14px; color: #5a6c7d;">货源审核</div>
                                        </a>
                                    </div>
                                </div>

                                <!-- 订单管理 -->
                                <div class="col-md-3 col-sm-6 col-xs-12">
                                    <div class="item audit-panel-soft" style="height: 120px;">
                                        <a href="{:url('order/index')}?status=pending" class="btn-dialog"
                                           title="订单管理" data-area='["80%","90%"]' icon="fa fa-shopping-cart fa-fw">
                                            <div class="inner">
                                                <h1 style="font-size: 36px; color: #dc3545;">{$pendingorder|htmlentities}</h1>
                                            </div>
                                            <div class="txt" style="font-size: 14px; color: #5a6c7d;">订单管理</div>
                                        </a>
                                    </div>
                                </div>

                                <!-- VIP订单管理 -->
                                <div class="col-md-3 col-sm-6 col-xs-12">
                                    <div class="item audit-panel-soft" style="height: 120px;">
                                        <a href="{:url('vip_order/index')}?status=pending" class="btn-dialog"
                                           title="VIP订单管理" data-area='["80%","90%"]' icon="fa fa-diamond fa-fw">
                                            <div class="inner">
                                                <h1 style="font-size: 36px; color: #6f42c1;">{$pendingviporder|htmlentities}</h1>
                                            </div>
                                            <div class="txt" style="font-size: 14px; color: #5a6c7d;">VIP订单管理</div>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="tab-pane fade" id="two">
                <div class="row">
                    <div class="col-xs-12">
                        {:__('Custom zone')}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* 柔和的审核面板样式 */
.audit-panel-soft {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 12px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.audit-panel-soft:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    border-color: #dee2e6;
}

.audit-panel-soft .btn-dialog {
    display: block;
    width: 100%;
    height: 100%;
    text-decoration: none;
    color: inherit;
    padding: 20px;
    border-radius: 12px;
}

.audit-panel-soft .inner {
    text-align: center;
    margin-bottom: 8px;
}

.audit-panel-soft .txt {
    text-align: center;
    font-weight: 500;
    letter-spacing: 0.5px;
}

/* 柔和的统计卡片背景 */
.modern-stat-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.modern-stat-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    border-color: #dee2e6;
}

/* 柔和的图表容器 */
.chart-container-modern {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 柔和的标题颜色 */
.chart-title-modern {
    color: #495057;
    font-weight: 600;
}

.chart-subtitle {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 0;
}

/* 统一的页面背景 */
.content-wrapper {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

/* 柔和的趋势指示器 */
.trend-indicator {
    font-size: 12px;
    color: #6c757d;
    margin-top: 4px;
}

.trend-up {
    color: #6c7f75;
}

.trend-down {
    color: #7f6c72;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .audit-panel-soft {
        margin-bottom: 15px;
    }

    .audit-panel-soft .inner h1 {
        font-size: 28px !important;
    }

    .audit-panel-soft .txt {
        font-size: 12px !important;
    }
}
</style>
