<?php

namespace app\api\controller;

use app\common\controller\Api;
use addons\epay\library\Service;
use think\Db;

/**
 * 货主认证接口
 */
class Goods extends Api
{
    protected $noNeedLogin = [];
    protected $noNeedRight = '*';

    /**
     * 个人认证申请
     *
     * @ApiMethod (POST)
     * @ApiParams (name="realname", type="string", required=true, description="真实姓名")
     * @ApiParams (name="idcard", type="string", required=true, description="身份证号")
     * @ApiParams (name="font_image", type="string", required=true, description="身份证正面")
     * @ApiParams (name="reverse_image", type="string", required=true, description="身份证反面")
     */
    public function userApply()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        // 检查是否已有认证记录
        $existing = Db::name('goods_audit')->where('user_id', $user->id)->find();
        if ($existing && $existing['status'] == 'pending') {
            $this->error('您已有审核中的申请，请等待审核结果');
        }
        if ($existing && $existing['status'] == 'pass') {
            $this->error('您已通过货主认证，无需重复申请');
        }

        $realname = $this->request->post('realname');
        $idcard = $this->request->post('idcard');
        $font_image = $this->request->post('font_image');
        $reverse_image = $this->request->post('reverse_image');

        // 验证必填参数
        if (!$realname || !$idcard || !$font_image || !$reverse_image) {
            $this->error(__('Invalid parameters'));
        }

        // 验证身份证号格式
        if (!preg_match('/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/', $idcard)) {
            $this->error('身份证号格式不正确');
        }

        $data = [
            'user_id' => $user->id,
            'kind' => 'user',
            'realname' => $realname,
            'idcard' => $idcard,
            'font_image' => $font_image,
            'reverse_image' => $reverse_image,
            'status' => 'pending',
            'createtime' => time()
        ];

        try {
            if ($existing && $existing['status'] == 'refuse') {
                // 如果之前被拒绝，更新记录
                Db::name('goods_audit')->where('id', $existing['id'])->update($data);
            } else {
                // 新增记录
                Db::name('goods_audit')->insert($data);
            }
        } catch (\Exception $e) {
            $this->error('申请提交失败，请重试');
        }
        $this->success('个人认证申请提交成功，请等待审核');
    }

    /**
     * 企业认证申请
     *
     * @ApiMethod (POST)
     * @ApiParams (name="license_image", type="string", required=true, description="营业执照照片")
     * @ApiParams (name="name", type="string", required=true, description="公司名称")
     * @ApiParams (name="code", type="string", required=true, description="统一社会信用代码")
     * @ApiParams (name="font_image", type="string", required=true, description="身份证正面")
     * @ApiParams (name="reverse_image", type="string", required=true, description="身份证反面")
     * @ApiParams (name="realname", type="string", required=true, description="法人真实姓名")
     * @ApiParams (name="idcard", type="string", required=true, description="法人身份证号")
     */
    public function companyApply()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        // 检查是否已有认证记录
        $existing = Db::name('goods_audit')->where('user_id', $user->id)->find();
        if ($existing && $existing['status'] == 'pending') {
            $this->error('您已有审核中的申请，请等待审核结果');
        }
        if ($existing && $existing['status'] == 'pass') {
            $this->error('您已通过货主认证，无需重复申请');
        }

        $license_image = $this->request->post('license_image');
        $name = $this->request->post('name');
        $code = $this->request->post('code');
        $font_image = $this->request->post('font_image');
        $reverse_image = $this->request->post('reverse_image');
        $realname = $this->request->post('realname');
        $idcard = $this->request->post('idcard');

        // 验证必填参数
        if (!$license_image || !$name || !$code || !$font_image || !$reverse_image || !$realname || !$idcard) {
            $this->error(__('Invalid parameters'));
        }

        // 验证统一社会信用代码格式（18位）
        if (!preg_match('/^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/', $code)) {
            $this->error('统一社会信用代码格式不正确');
        }

        // 验证身份证号格式
        if (!preg_match('/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/', $idcard)) {
            $this->error('法人身份证号格式不正确');
        }

        // 检查统一社会信用代码是否已存在
        $existingCode = Db::name('goods_audit')->where('code', $code)->where('status', 'pass')->find();
        if ($existingCode) {
            $this->error('该统一社会信用代码已被使用');
        }

        $data = [
            'user_id' => $user->id,
            'kind' => 'company',
            'license_image' => $license_image,
            'name' => $name,
            'code' => $code,
            'font_image' => $font_image,
            'reverse_image' => $reverse_image,
            'realname' => $realname,
            'idcard' => $idcard,
            'status' => 'pending',
            'createtime' => time()
        ];

        try {
            if ($existing && $existing['status'] == 'refuse') {
                // 如果之前被拒绝，更新记录
                Db::name('goods_audit')->where('id', $existing['id'])->update($data);
            } else {
                // 新增记录
                Db::name('goods_audit')->insert($data);
            }
        } catch (\Exception $e) {
            $this->error('申请提交失败，请重试');
        }
        $this->success('货主认证申请提交成功，请等待审核');
    }

    /**
     * 获取认证详情
     *
     * @ApiMethod (GET)
     */
    public function auditInfo()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $audit = Db::name('goods_audit')->where('user_id', $user->id)->find();
        if (!$audit) {
            $this->success('获取成功', ['has_apply' => false]);
        }

        $this->success('获取成功', [
            'has_apply' => true,
            'audit' => $audit
        ]);
    }

    /**
     * 编辑认证信息（审核拒绝后可编辑）
     *
     * @ApiMethod (POST)
     * @ApiParams (name="kind", type="string", required=true, description="认证类型:user=个人认证,company=企业认证")
     * @ApiParams (name="license_image", type="string", required=false, description="营业执照照片（企业认证必填）")
     * @ApiParams (name="name", type="string", required=false, description="公司名称（企业认证必填）")
     * @ApiParams (name="code", type="string", required=false, description="统一社会信用代码（企业认证必填）")
     * @ApiParams (name="font_image", type="string", required=true, description="身份证正面")
     * @ApiParams (name="reverse_image", type="string", required=true, description="身份证反面")
     * @ApiParams (name="realname", type="string", required=true, description="真实姓名")
     * @ApiParams (name="idcard", type="string", required=true, description="身份证号")
     */
    public function auditEdit()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        // 检查认证记录
        $audit = Db::name('goods_audit')->where('user_id', $user->id)->find();
        if (!$audit) {
            $this->error('您还没有货主认证记录，请先申请认证');
        }

        // 只有审核拒绝的记录才能编辑
        if ($audit['status'] != 'refuse') {
            $this->error('只有审核拒绝的申请才能编辑');
        }

        $kind = $this->request->post('kind');
        $font_image = $this->request->post('font_image');
        $reverse_image = $this->request->post('reverse_image');
        $realname = $this->request->post('realname');
        $idcard = $this->request->post('idcard');

        // 验证必填参数
        if (!$kind || !$font_image || !$reverse_image || !$realname || !$idcard) {
            $this->error(__('Invalid parameters'));
        }

        // 验证认证类型
        if (!in_array($kind, ['user', 'company'])) {
            $this->error('认证类型参数错误');
        }

        // 验证身份证号格式
        if (!preg_match('/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/', $idcard)) {
            $this->error('身份证号格式不正确');
        }

        $data = [
            'kind' => $kind,
            'font_image' => $font_image,
            'reverse_image' => $reverse_image,
            'realname' => $realname,
            'idcard' => $idcard,
            'status' => 'pending',
            'createtime' => time()
        ];

        // 如果是企业认证，需要额外的企业信息
        if ($kind == 'company') {
            $license_image = $this->request->post('license_image');
            $name = $this->request->post('name');
            $code = $this->request->post('code');

            if (!$license_image || !$name || !$code) {
                $this->error('企业认证信息不完整');
            }

            // 验证统一社会信用代码格式
            if (!preg_match('/^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/', $code)) {
                $this->error('统一社会信用代码格式不正确');
            }

            // 检查统一社会信用代码是否已被其他用户使用
            $existingCode = Db::name('goods_audit')->where('code', $code)->where('status', 'pass')->where('id', '<>', $audit['id'])->find();
            if ($existingCode) {
                $this->error('该统一社会信用代码已被使用');
            }

            $data['license_image'] = $license_image;
            $data['name'] = $name;
            $data['code'] = $code;
        } else {
            // 个人认证时清空企业相关字段
            $data['license_image'] = null;
            $data['name'] = null;
            $data['code'] = null;
        }
        try {
            Db::name('goods_audit')->where('id', $audit['id'])->update($data);
        } catch (\Exception $e) {
            $this->error('修改失败，请重试');
        }
        $this->success('认证信息修改成功，请等待重新审核');
    }

    /**
     * 计算两点间距离（单位：公里）
     * 使用 Haversine 公式
     */
    private function calculateDistance($lat1, $lng1, $lat2, $lng2)
    {
        if (!$lat1 || !$lng1 || !$lat2 || !$lng2) {
            return 0;
        }

        $earthRadius = 6371; // 地球半径，单位：公里

        $lat1 = deg2rad($lat1);
        $lng1 = deg2rad($lng1);
        $lat2 = deg2rad($lat2);
        $lng2 = deg2rad($lng2);

        $deltaLat = $lat2 - $lat1;
        $deltaLng = $lng2 - $lng1;

        $a = sin($deltaLat / 2) * sin($deltaLat / 2) + cos($lat1) * cos($lat2) * sin($deltaLng / 2) * sin($deltaLng / 2);
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return round($earthRadius * $c, 2);
    }

    /**
     * 生成订单号
     */
    private function generateOrderId()
    {
        return 'GD' . date('YmdHis') . rand(1000, 9999);
    }

    /**
     * 创建货源订单
     *
     * @ApiMethod (POST)
     * @ApiParams (name="car_type", type="string", required=true, description="发车类型:all=整车,pin=拼车")
     * @ApiParams (name="time_type", type="string", required=true, description="时效要求:normal=常规,danger=紧急")
     * @ApiParams (name="get_address_id", type="integer", required=true, description="装货地址ID")
     * @ApiParams (name="send_address_id", type="integer", required=true, description="卸货地址ID")
     * @ApiParams (name="get_date", type="string", required=true, description="装货日期")
     * @ApiParams (name="get_datetime", type="string", required=false, description="装货时间")
     * @ApiParams (name="send_date", type="string", required=false, description="卸货日期")
     * @ApiParams (name="send_datetime", type="string", required=false, description="卸货时间")
     * @ApiParams (name="goods_type", type="string", required=true, description="货物类型")
     * @ApiParams (name="weight", type="string", required=false, description="预估重量")
     * @ApiParams (name="volume", type="string", required=false, description="预估体积")
     * @ApiParams (name="long", type="string", required=false, description="车长信息")
     * @ApiParams (name="remark", type="string", required=false, description="用车备注")
     * @ApiParams (name="mobile", type="string", required=true, description="货主联系电话")
     * @ApiParams (name="goods_car_id", type="integer", required=false, description="装货车型ID")
     */
    public function createOrder()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        // 检查货主认证状态
        $audit = Db::name('goods_audit')->where('user_id', $user->id)->where('status', 'pass')->find();
        if (!$audit) {
            $this->error('请先完成货主认证');
        }

        // 获取参数
        $car_type = $this->request->post('car_type');
        $time_type = $this->request->post('time_type');
        $get_address_id = $this->request->post('get_address_id');
        $send_address_id = $this->request->post('send_address_id');
        $get_date = $this->request->post('get_date');
        $get_datetime = $this->request->post('get_datetime');
        $send_date = $this->request->post('send_date');
        $send_datetime = $this->request->post('send_datetime');
        $goods_type = $this->request->post('goods_type');
        $weight = $this->request->post('weight');
        $volume = $this->request->post('volume');
        $long = $this->request->post('long');
        $remark = $this->request->post('remark');
        $mobile = $this->request->post('mobile');
        $goods_car_id = $this->request->post('goods_car_id');

        // 验证必填参数
        if (!$car_type || !$time_type || !$get_address_id || !$send_address_id || !$get_date || !$goods_type || !$mobile) {
            $this->error(__('Invalid parameters'));
        }

        // 验证枚举值
        if (!in_array($car_type, ['all', 'pin'])) {
            $this->error('发车类型参数错误');
        }
        if (!in_array($time_type, ['normal', 'danger'])) {
            $this->error('时效要求参数错误');
        }

        // 验证手机号
        if (!preg_match('/^1[3-9]\d{9}$/', $mobile)) {
            $this->error('请输入正确的手机号');
        }

        // 获取地址信息并计算距离
        $getAddress = Db::name('address')->where('id', $get_address_id)->find();
        $sendAddress = Db::name('address')->where('id', $send_address_id)->find();

        if (!$getAddress || !$sendAddress) {
            $this->error('地址信息不存在');
        }

        // 计算距离
        $distance = $this->calculateDistance(
            $getAddress['lat'],
            $getAddress['lng'],
            $sendAddress['lat'],
            $sendAddress['lng']
        );

        $data = [
            'user_id' => $user->id,
            'car_type' => $car_type,
            'time_type' => $time_type,
            'get_address_id' => $get_address_id,
            'send_address_id' => $send_address_id,
            'get_date' => $get_date,
            'get_datetime' => $get_datetime,
            'send_date' => $send_date,
            'send_datetime' => $send_datetime,
            'goods_type' => $goods_type,
            'weight' => $weight,
            'volume' => $volume,
            'long' => $long,
            'remark' => $remark,
            'mobile' => $mobile,
            'goods_car_id' => $goods_car_id ? intval($goods_car_id) : null,
            'orderid' => $this->generateOrderId(),
            'status' => 'pending',
            'distance' => $distance,
            'createtime' => time()
        ];

        try {
            $id = Db::name('goods')->insertGetId($data);
        } catch (\Exception $e) {
            $this->error('订单创建失败，请重试');
        }

        $this->success('货源订单创建成功', ['id' => $id, 'orderid' => $data['orderid']]);
    }

    /**
     * 货源订单支付
     *
     * @ApiMethod (POST)
     * @ApiParams (name="orderid", type="string", required=true, description="订单号")
     * @ApiParams (name="pay_type", type="string", required=true, description="支付方式:wechat=微信,alipay=支付宝")
     * @ApiParams (name="test", type="integer", required=false, description="测试模式:1=测试,0=正式")
     */
    public function payOrder()
    {
        $orderid = $this->request->post('orderid');
        $pay_type = $this->request->post('pay_type');
        $test = $this->request->post('test', 0);

        // 验证必填参数
        if (!$orderid || !$pay_type) {
            $this->error(__('Invalid parameters'));
        }

        // 验证支付方式
        if (!in_array($pay_type, ['wechat', 'alipay'])) {
            $this->error('支付方式参数错误');
        }

        // 查询订单信息
        $order = Db::name('goods')->where('orderid', $orderid)->find();
        if (!$order) {
            $this->error('订单不存在');
        }

        // 检查订单状态
        if ($order['status'] == 'audit') {
            $this->error('订单已支付，正在审核中');
        }
        if ($order['status'] == 'pass') {
            $this->error('订单已通过审核');
        }

        // 重新生成订单号（防止第三方支付重复提交）
        $newOrderid = 'GD' . date('YmdHis') . rand(1000, 9999);

        try {
            // 更新订单号
            Db::name('goods')->where('id', $order['id'])->update([
                'orderid' => $newOrderid
            ]);
            $order['orderid'] = $newOrderid; // 更新本地变量
        } catch (\Exception $e) {
            $this->error('订单更新失败，请重试');
        }

        // 测试模式直接回调
        if ($test) {
            $this->payCallback($order['orderid'], '88888888', $pay_type);
            $this->success('支付成功');
        }

        // 微信支付需要openid
        if ($pay_type == 'wechat' && !$openid = $this->auth->openid) {
            $this->error('请先绑定微信');
        }

        // 设置支付金额，如果为0则设置为0.01元
        $amount = $order['amount'] > 0 ? $order['amount'] : 0.01;

        $notifyurl = $this->request->domain() . '/api/goods/notifyx/paytype/' . $pay_type;

        // 调用支付接口
        try {
            $pay = Service::submitOrder($amount, $order['orderid'], $pay_type, '货源发布', $notifyurl, null, 'app', $openid ?? '');
        } catch (\Exception $e) {
            $this->error('支付失败，请重试');
        }

        if ($pay) {
            $this->success('支付成功', $pay);
        } else {
            $this->error('支付失败');
        }
    }

    /**
     * 支付成功回调，仅供开发测试
     */
    public function notifyx()
    {
        $paytype = $this->request->param('paytype');
        $pay = Service::checkNotify($paytype);
        if (!$pay) {
            return json(['code' => 'FAIL', 'message' => '失败'], 500, ['Content-Type' => 'application/json']);
        }

        // 获取回调数据，V3和V2的回调接收不同
        $data = Service::isVersionV3() ? $pay->callback() : $pay->verify();
        try {
            //微信支付V3返回和V2不同
            if (Service::isVersionV3() && $paytype === 'wechat') {
                $data = $data['resource']['ciphertext'];
                $data['total_fee'] = $data['amount']['total'];
            }
            \think\Log::record($data);
            //获取支付金额、订单号
            $payamount = $paytype == 'alipay' ? $data['total_amount'] : $data['total_fee'] / 100;
            $out_trade_no = $data['out_trade_no'];
            $this->payCallback($out_trade_no, $data['transaction_id'], $paytype);
            \think\Log::record("回调成功，订单号：{$out_trade_no}，金额：{$payamount}");
        } catch (\Exception $e) {
            \think\Log::record("回调逻辑处理错误:" . $e->getMessage(), "error");
        }

        //下面这句必须要执行,且在此之前不能有任何输出
        if (Service::isVersionV3()) {
            return $pay->success()->getBody()->getContents();
        } else {
            return $pay->success()->send();
        }
    }

    /**
     * 支付回调处理
     *
     * @ApiMethod (POST)
     * @ApiParams (name="orderid", type="string", required=true, description="订单号")
     * @ApiParams (name="trade_no", type="string", required=true, description="第三方交易号")
     * @ApiParams (name="pay_type", type="string", required=true, description="支付方式")
     */
    public function payCallback($orderid, $trade_no, $pay_type)
    {
        // 验证必填参数
        if (!$orderid || !$trade_no || !$pay_type) {
            $this->error(__('Invalid parameters'));
        }

        // 查询订单
        $order = Db::name('goods')->where('orderid', $orderid)->find();
        if (!$order) {
            $this->error('订单不存在');
        }

        // 检查订单状态
        if ($order['status'] == 'audit') {
            $this->success('订单已支付');
        }

        try {
            Db::startTrans();
            // 更新订单状态
            Db::name('goods')->where('id', $order['id'])->update([
                'status' => 'audit',
                'paytime' => time(),
                'paytype' => $pay_type,
                'transid' => $trade_no
            ]);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->error('支付处理失败');
        }

        $this->success('支付处理成功');
    }

    /**
     * 获取货源列表
     *
     * @ApiMethod (GET)
     * @ApiParams (name="car_type", type="string", required=false, description="发车类型筛选:all=整车,pin=拼车")
     * @ApiParams (name="time_type", type="string", required=false, description="时效要求筛选:normal=常规,danger=紧急")
     * @ApiParams (name="goods_type", type="string", required=false, description="货物类型筛选")
     * @ApiParams (name="page", type="integer", required=false, description="页码，默认1")
     * @ApiParams (name="limit", type="integer", required=false, description="每页数量，默认10")
     * @ApiParams (name="lat", type="string", required=false, description="当前用户纬度")
     * @ApiParams (name="lng", type="string", required=false, description="当前用户经度")
     */
    public function getGoodsList()
    {
        $car_type = $this->request->get('car_type');
        $time_type = $this->request->get('time_type');
        $goods_type = $this->request->get('goods_type');
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);
        $userLat = $this->request->get('lat');
        $userLng = $this->request->get('lng');

        $where = ['g.status' => 'pass', 'g.is_show' => 'normal'];

        if ($car_type && in_array($car_type, ['all', 'pin'])) {
            $where['g.car_type'] = $car_type;
        }
        if ($time_type && in_array($time_type, ['normal', 'danger'])) {
            $where['g.time_type'] = $time_type;
        }
        if ($goods_type) {
            $where['g.goods_type'] = ['like', '%' . $goods_type . '%'];
        }

        $list = Db::name('goods')
            ->alias('g')
            ->join('address ga', 'g.get_address_id = ga.id', 'INNER')
            ->join('address sa', 'g.send_address_id = sa.id', 'INNER')
            ->join('user u', 'g.user_id = u.id', 'LEFT')
            ->join('goods_car gc', 'g.goods_car_id = gc.id', 'LEFT')
            ->field('g.*, ga.address as get_address, ga.lng as get_lng, ga.lat as get_lat,
                     sa.address as send_address, sa.lng as send_lng, sa.lat as send_lat,
                     u.nickname, u.avatar, gc.name as car_name')
            ->where($where)
            ->page($page, $limit)
            ->select();

        $list = $list ? $list->toArray() : [];

        $total = Db::name('goods')
            ->alias('g')
            ->join('address ga', 'g.get_address_id = ga.id', 'INNER')
            ->join('address sa', 'g.send_address_id = sa.id', 'INNER')
            ->where($where)
            ->count();

        // 计算距离并排序
        foreach ($list as &$item) {
            $item['createtime_text'] = date('Y-m-d H:i:s', $item['createtime']);
            $item['car_type_text'] = $item['car_type'] == 'all' ? '整车' : '拼车';
            $item['time_type_text'] = $item['time_type'] == 'normal' ? '常规' : '紧急';

            // 计算用户到装货地的距离
            if ($userLat && $userLng && $item['get_lat'] && $item['get_lng']) {
                $item['user_distance'] = $this->calculateDistance($userLat, $userLng, $item['get_lat'], $item['get_lng']);
            } else {
                $item['user_distance'] = 0;
            }
        }

        // 按距离排序（由近到远）
        if ($userLat && $userLng) {
            usort($list, function ($a, $b) {
                return $a['user_distance'] <=> $b['user_distance'];
            });
        }

        $this->success('获取成功', [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit
        ]);
    }

    /**
     * 获取货源详情
     *
     * @ApiMethod (GET)
     * @ApiParams (name="id", type="integer", required=true, description="货源ID")
     * @ApiParams (name="lat", type="string", required=false, description="当前用户纬度")
     * @ApiParams (name="lng", type="string", required=false, description="当前用户经度")
     */
    public function getGoodsDetail()
    {
        $id = $this->request->get('id');
        $userLat = $this->request->get('lat');
        $userLng = $this->request->get('lng');

        if (!$id) {
            $this->error(__('Invalid parameters'));
        }

        $goods = Db::name('goods')
            ->alias('g')
            ->join('address ga', 'g.get_address_id = ga.id', 'INNER')
            ->join('address sa', 'g.send_address_id = sa.id', 'INNER')
            ->join('user u', 'g.user_id = u.id', 'LEFT')
            ->join('goods_car gc', 'g.goods_car_id = gc.id', 'LEFT')
            ->field('g.*, ga.address as get_address, ga.lng as get_lng, ga.lat as get_lat,
                     ga.username as get_username, ga.mobile as get_mobile, ga.detail as get_detail,
                     sa.address as send_address, sa.lng as send_lng, sa.lat as send_lat,
                     sa.username as send_username, sa.mobile as send_mobile, sa.detail as send_detail,
                     u.nickname, u.avatar, gc.name as car_name')
            ->where(['g.id' => $id, 'g.status' => 'pass', 'g.is_show' => 'normal'])
            ->find();

        if (!$goods) {
            $this->error('货源信息不存在');
        }

        // 格式化数据
        $goods['createtime_text'] = date('Y-m-d H:i:s', $goods['createtime']);
        $goods['car_type_text'] = $goods['car_type'] == 'all' ? '整车' : '拼车';
        $goods['time_type_text'] = $goods['time_type'] == 'normal' ? '常规' : '紧急';

        // 计算用户到装货地的距离
        if ($userLat && $userLng && $goods['get_lat'] && $goods['get_lng']) {
            $goods['user_distance'] = $this->calculateDistance($userLat, $userLng, $goods['get_lat'], $goods['get_lng']);
        } else {
            $goods['user_distance'] = 0;
        }

        $this->success('获取成功', $goods);
    }

    /**
     * 货源上下架
     *
     * @ApiMethod (POST)
     * @ApiParams (name="id", type="integer", required=true, description="货源ID")
     * @ApiParams (name="is_show", type="string", required=true, description="状态:normal=上架,hidden=下架")
     */
    public function toggleShow()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $id = $this->request->post('id');
        $is_show = $this->request->post('is_show');

        if (!$id || !$is_show) {
            $this->error(__('Invalid parameters'));
        }

        if (!in_array($is_show, ['normal', 'hidden'])) {
            $this->error('状态参数错误');
        }

        // 检查货源是否属于当前用户
        $goods = Db::name('goods')->where(['id' => $id, 'user_id' => $user->id])->find();
        if (!$goods) {
            $this->error('货源信息不存在或无权限操作');
        }

        try {
            Db::name('goods')->where('id', $id)->update(['is_show' => $is_show]);
        } catch (\Exception $e) {
            $this->error('操作失败，请重试');
        }

        $action = $is_show == 'normal' ? '上架' : '下架';
        $this->success('货源' . $action . '成功');
    }

    /**
     * 编辑货源信息
     *
     * @ApiMethod (POST)
     * @ApiParams (name="id", type="integer", required=true, description="货源ID")
     * @ApiParams (name="car_type", type="string", required=true, description="发车类型:all=整车,pin=拼车")
     * @ApiParams (name="time_type", type="string", required=true, description="时效要求:normal=常规,danger=紧急")
     * @ApiParams (name="get_address_id", type="integer", required=true, description="装货地址ID")
     * @ApiParams (name="send_address_id", type="integer", required=true, description="卸货地址ID")
     * @ApiParams (name="get_date", type="string", required=true, description="装货日期")
     * @ApiParams (name="get_datetime", type="string", required=false, description="装货时间")
     * @ApiParams (name="send_date", type="string", required=false, description="卸货日期")
     * @ApiParams (name="send_datetime", type="string", required=false, description="卸货时间")
     * @ApiParams (name="goods_type", type="string", required=true, description="货物类型")
     * @ApiParams (name="weight", type="string", required=false, description="预估重量")
     * @ApiParams (name="volume", type="string", required=false, description="预估体积")
     * @ApiParams (name="long", type="string", required=false, description="车长信息")
     * @ApiParams (name="remark", type="string", required=false, description="用车备注")
     * @ApiParams (name="mobile", type="string", required=true, description="货主联系电话")
     * @ApiParams (name="goods_car_id", type="integer", required=false, description="装货车型ID")
     */
    public function editGoods()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $id = $this->request->post('id');
        if (!$id) {
            $this->error(__('Invalid parameters'));
        }

        // 检查货源是否属于当前用户
        $goods = Db::name('goods')->where(['id' => $id, 'user_id' => $user->id])->find();
        if (!$goods) {
            $this->error('货源信息不存在或无权限操作');
        }

        // 只有待支付和审核拒绝的订单可以编辑
        if (!in_array($goods['status'], ['pending', 'refuse'])) {
            $this->error('当前状态不允许编辑');
        }

        // 获取参数
        $car_type = $this->request->post('car_type');
        $time_type = $this->request->post('time_type');
        $get_address_id = $this->request->post('get_address_id');
        $send_address_id = $this->request->post('send_address_id');
        $get_date = $this->request->post('get_date');
        $get_datetime = $this->request->post('get_datetime');
        $send_date = $this->request->post('send_date');
        $send_datetime = $this->request->post('send_datetime');
        $goods_type = $this->request->post('goods_type');
        $weight = $this->request->post('weight');
        $volume = $this->request->post('volume');
        $long = $this->request->post('long');
        $remark = $this->request->post('remark');
        $mobile = $this->request->post('mobile');
        $goods_car_id = $this->request->post('goods_car_id');

        // 验证必填参数
        if (!$car_type || !$time_type || !$get_address_id || !$send_address_id || !$get_date || !$goods_type || !$mobile) {
            $this->error(__('Invalid parameters'));
        }

        // 验证枚举值
        if (!in_array($car_type, ['all', 'pin'])) {
            $this->error('发车类型参数错误');
        }
        if (!in_array($time_type, ['normal', 'danger'])) {
            $this->error('时效要求参数错误');
        }

        // 验证手机号
        if (!preg_match('/^1[3-9]\d{9}$/', $mobile)) {
            $this->error('请输入正确的手机号');
        }

        // 获取地址信息并重新计算距离
        $getAddress = Db::name('address')->where('id', $get_address_id)->find();
        $sendAddress = Db::name('address')->where('id', $send_address_id)->find();

        if (!$getAddress || !$sendAddress) {
            $this->error('地址信息不存在');
        }

        // 重新计算距离
        $distance = $this->calculateDistance(
            $getAddress['lat'],
            $getAddress['lng'],
            $sendAddress['lat'],
            $sendAddress['lng']
        );

        $data = [
            'car_type' => $car_type,
            'time_type' => $time_type,
            'get_address_id' => $get_address_id,
            'send_address_id' => $send_address_id,
            'get_date' => $get_date,
            'get_datetime' => $get_datetime,
            'send_date' => $send_date,
            'send_datetime' => $send_datetime,
            'goods_type' => $goods_type,
            'weight' => $weight,
            'volume' => $volume,
            'long' => $long,
            'remark' => $remark,
            'mobile' => $mobile,
            'goods_car_id' => $goods_car_id ? intval($goods_car_id) : null,
            'distance' => $distance,
            'status' => $goods['status'] == 'refuse' ? 'audit' : 'pending', // 编辑后重置为审核中或待支付状态
            'is_show' => "hidden"
        ];

        try {
            Db::name('goods')->where('id', $id)->update($data);
        } catch (\Exception $e) {
            $this->error('编辑失败，请重试');
        }

        $this->success('货源信息编辑成功');
    }

    /**
     * 获取我发布的货源列表
     *
     * @ApiMethod (GET)
     * @ApiParams (name="status", type="string", required=false, description="状态筛选:pending=待支付,audit=审核中,pass=通过,refuse=拒绝")
     * @ApiParams (name="page", type="integer", required=false, description="页码，默认1")
     * @ApiParams (name="limit", type="integer", required=false, description="每页数量，默认10")
     */
    public function getMyGoodsList()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $status = $this->request->get('status');
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);

        $where = ['g.user_id' => $user->id];
        if ($status && in_array($status, ['pending', 'audit', 'pass', 'refuse'])) {
            $where['g.status'] = $status;
        }

        $list = Db::name('goods')
            ->alias('g')
            ->join('address ga', 'g.get_address_id = ga.id', 'INNER')
            ->join('address sa', 'g.send_address_id = sa.id', 'INNER')
            ->field('g.*, ga.address as get_address, sa.address as send_address')
            ->where($where)
            ->order('g.createtime desc')
            ->page($page, $limit)
            ->select();

        $total = Db::name('goods')->alias('g')->where($where)->count();

        // 格式化数据
        foreach ($list as &$item) {
            $item['createtime_text'] = date('Y-m-d H:i:s', $item['createtime']);
            $item['car_type_text'] = $item['car_type'] == 'all' ? '整车' : '拼车';
            $item['time_type_text'] = $item['time_type'] == 'normal' ? '常规' : '紧急';
            $item['is_show_text'] = $item['is_show'] == 'normal' ? '上架' : '下架';

            // 状态文本
            $statusText = [
                'pending' => '待支付',
                'audit' => '审核中',
                'pass' => '已通过',
                'refuse' => '已拒绝'
            ];
            $item['status_text'] = $statusText[$item['status']] ?? '未知状态';
        }

        $this->success('获取成功', [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit
        ]);
    }

    /**
     * 获取我发布的货源详情
     *
     * @ApiMethod (GET)
     * @ApiParams (name="id", type="integer", required=true, description="货源ID")
     */
    public function getMyGoodsDetail()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $id = $this->request->get('id');
        if (!$id) {
            $this->error(__('Invalid parameters'));
        }

        $goods = Db::name('goods')
            ->alias('g')
            ->join('address ga', 'g.get_address_id = ga.id', 'INNER')
            ->join('address sa', 'g.send_address_id = sa.id', 'INNER')
            ->field('g.*, ga.address as get_address, ga.lng as get_lng, ga.lat as get_lat,
                     ga.username as get_username, ga.mobile as get_mobile, ga.detail as get_detail,
                     sa.address as send_address, sa.lng as send_lng, sa.lat as send_lat,
                     sa.username as send_username, sa.mobile as send_mobile, sa.detail as send_detail')
            ->where(['g.id' => $id, 'g.user_id' => $user->id])
            ->find();

        if (!$goods) {
            $this->error('货源信息不存在或无权限查看');
        }

        // 格式化数据
        $goods['createtime_text'] = date('Y-m-d H:i:s', $goods['createtime']);
        $goods['paytime_text'] = $goods['paytime'] ? date('Y-m-d H:i:s', $goods['paytime']) : '';
        $goods['car_type_text'] = $goods['car_type'] == 'all' ? '整车' : '拼车';
        $goods['time_type_text'] = $goods['time_type'] == 'normal' ? '常规' : '紧急';
        $goods['is_show_text'] = $goods['is_show'] == 'normal' ? '上架' : '下架';

        // 状态文本
        $statusText = [
            'pending' => '待支付',
            'audit' => '审核中',
            'pass' => '已通过',
            'refuse' => '已拒绝'
        ];
        $goods['status_text'] = $statusText[$goods['status']] ?? '未知状态';

        $this->success('获取成功', $goods);
    }
}
