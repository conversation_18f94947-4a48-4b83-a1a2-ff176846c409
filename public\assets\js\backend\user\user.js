define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'user/user/index',
                    add_url: 'user/user/add',
                    edit_url: 'user/user/edit',
                    del_url: 'user/user/del',
                    multi_url: 'user/user/multi',
                    table: 'user',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'user.id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        { checkbox: true },
                        { field: 'id', title: __('Id'), sortable: true },
                        { field: 'username', title: __('Username'), operate: 'LIKE' },
                        { field: 'nickname', title: __('Nickname'), operate: 'LIKE' },
                        { field: 'mobile', title: __('Mobile'), operate: 'LIKE' },
                        { field: 'avatar', title: __('Avatar'), events: Table.api.events.image, formatter: Table.api.formatter.image, operate: false },
                        { field: 'gender', title: __('Gender'), visible: false, searchList: { 1: __('Male'), 0: __('Female') } },
                        { field: 'successions', title: __('Successions'), visible: false, operate: 'BETWEEN', sortable: true },
                        { field: 'maxsuccessions', title: __('Maxsuccessions'), visible: false, operate: 'BETWEEN', sortable: true },
                        { field: 'logintime', title: __('Logintime'), formatter: Table.api.formatter.datetime, operate: 'RANGE', addclass: 'datetimerange', sortable: true },
                        { field: 'loginip', title: __('Loginip'), formatter: Table.api.formatter.search },
                        { field: 'jointime', title: __('Jointime'), formatter: Table.api.formatter.datetime, operate: 'RANGE', addclass: 'datetimerange', sortable: true },
                        { field: 'joinip', title: __('Joinip'), formatter: Table.api.formatter.search },
                        { field: 'endtime', title: __('VIP Endtime'), formatter: Table.api.formatter.datetime, operate: 'RANGE', addclass: 'datetimerange', sortable: true, visible: false },
                        {
                            field: 'is_vip', title: __('VIP Status'), searchList: { 0: __('Normal'), 1: __('VIP') }, formatter: function (value, row, index) {
                                if (value == 1) {
                                    var endtime = row.endtime > 0 ? new Date(row.endtime * 1000).toLocaleString() : '';
                                    return '<span class="label label-success">VIP</span>' + (endtime ? '<br><small>' + endtime + '</small>' : '');
                                } else {
                                    return '<span class="label label-default">普通用户</span>';
                                }
                            }
                        },
                        { field: 'status', title: __('Status'), formatter: Table.api.formatter.status, searchList: { normal: __('Normal'), hidden: __('Hidden') } },
                        {
                            field: 'operate',
                            width: "120px",
                            title: __('Operate'),
                            table: table,
                            events: Table.api.events.operate,
                            buttons: [
                                {
                                    name: 'vip',
                                    title: __('VIP Management'),
                                    text: __('VIP'),
                                    classname: 'btn btn-xs btn-warning btn-dialog',
                                    extend: 'data-toggle="tooltip"',
                                    icon: 'fa fa-diamond',
                                    url: 'user/user/vip?user_id={id}',
                                    callback: function (data) {
                                        $("#table").bootstrapTable('refresh');
                                    },
                                    visible: function (row) {
                                        return true;
                                    }
                                }
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        vip: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});