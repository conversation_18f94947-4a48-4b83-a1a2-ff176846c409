<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Orderid')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-orderid" data-rule="required" class="form-control" name="row[orderid]" type="text" value="{$row.orderid|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-rule="required" data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[user_id]" type="text" value="{$row.user_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Vip_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-vip_id" data-rule="required" data-source="vip/index" class="form-control selectpage" name="row[vip_id]" type="text" value="{$row.vip_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Vip_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-vip_name" class="form-control" name="row[vip_name]" type="text" value="{$row.vip_name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Vip_day')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-vip_day" data-rule="required" class="form-control" name="row[vip_day]" type="number" value="{$row.vip_day|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Paytype')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-paytype" class="form-control" name="row[paytype]" type="text" value="{$row.paytype|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Vip_price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-vip_price" data-rule="required" class="form-control" step="0.01" name="row[vip_price]" type="number" value="{$row.vip_price|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Amount')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-amount" data-rule="required" class="form-control" step="0.01" name="row[amount]" type="number" value="{$row.amount|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Paytime')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-paytime" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[paytime]" type="text" value="{:$row.paytime?datetime($row.paytime):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Transid')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-transid" class="form-control" name="row[transid]" type="text" value="{$row.transid|htmlentities}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
