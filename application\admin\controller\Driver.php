<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use think\Db;
use think\exception\PDOException;
use Exception;

/**
 * 司机认证
 *
 * @icon fa fa-circle-o
 */
class Driver extends Backend
{

    /**
     * Driver模型对象
     * @var \app\admin\model\Driver
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Driver;

    }



    /**
     * 删除
     */
    public function del($ids = "")
    {
        if (!$this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ? $ids : $this->request->post("ids");
        if ($ids) {
            $pk = $this->model->getPk();
            $adminIds = $this->getDataLimitAdminIds();
            if (is_array($adminIds)) {
                $this->model->where($this->dataLimitField, 'in', $adminIds);
            }
            $list = $this->model->where($pk, 'in', $ids)->select();

            $count = 0;
            Db::startTrans();
            try {
                foreach ($list as $row) {
                    $userId = $row->user_id;

                    // 删除司机认证记录
                    $result = $row->delete();
                    if ($result) {
                        $count++;

                        // 同步删除相关的审核内容
                        $this->deleteRelatedAuthData($userId);
                    }
                }
                Db::commit();
            } catch (PDOException $e) {
                Db::rollback();
                $this->error($e->getMessage());
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            if ($count) {
                $this->success();
            } else {
                $this->error(__('No rows were deleted'));
            }
        }
        $this->error(__('Parameter %s can not be empty', 'ids'));
    }

    /**
     * 删除相关的审核内容
     * @param int $userId 用户ID
     */
    private function deleteRelatedAuthData($userId)
    {
        // 删除各种认证表中的数据
        $tables = [
            'driver_card',      // 身份证认证
            'driver_license',   // 驾驶证认证
            'driver_driving',   // 行驶证认证
            'driver_work',      // 从业资格证认证
            'driver_car'        // 人车合影认证
        ];

        foreach ($tables as $table) {
            try {
                Db::name($table)->where('user_id', $userId)->delete();
            } catch (Exception $e) {
                // 记录错误但不中断删除流程
                \think\Log::error("删除{$table}表数据失败: " . $e->getMessage());
            }
        }
    }

    /**
     * 批量删除
     */
    public function multi($ids = "")
    {
        if (!$this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ? $ids : $this->request->post("ids");
        $action = $this->request->post("action");
        if ($action == 'del') {
            $this->del($ids);
        } else {
            $this->error(__('Parameter error'));
        }
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    ->with(['user'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                
                $row->getRelation('user')->visible(['nickname']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

}
