<?php

return [
    'Id'             => '订单ID',
    'Orderid'        => '订单号',
    'User_id'        => '用户ID',
    'Vip_id'         => 'VIP套餐ID',
    'Vip_name'       => 'VIP套餐名称',
    'Vip_day'        => 'VIP时效(天)',
    'Createtime'     => '创建时间',
    'Status'         => '支付状态',
    'Status pending' => '待支付',
    'Set status to pending'=> '设为待支付',
    'Status paid'    => '已支付',
    'Set status to paid'=> '设为已支付',
    'Paytype'        => '支付方式',
    'Vip_price'      => '应付金额',
    'Amount'         => '实付金额',
    'Paytime'        => '支付时间',
    'Transid'        => '流水号',
    'User.nickname'  => '昵称'
];
