<?php

return [
    'Car_type'        => '发车类型',
    'Time_type'       => '时效要求',
    'Get_address_id'  => '装货地址',
    'Send_address_id' => '卸货地址',
    'Get_date'        => '装货日期',
    'Get_datetime'    => '装货时间',
    'Send_date'       => '卸货日期',
    'Send_datetime'   => '卸货时间',
    'Goods_type'      => '货物类型',
    'Weight'          => '预估重量',
    'Volume'          => '预估体积',
    'Long'            => '车长信息',
    'Remark'          => '用车备注',
    'Mobile'          => '货主联系电话',
    'Createtime'      => '创建时间',
    'User_id'         => '用户',
    'Orderid'         => '订单编号',
    'Status'          => '支付状态',
    'Status pending'  => '待支付',
    'Set status to pending'=> '设为待支付',
    'Status audit'    => '审核中',
    'Set status to audit'=> '设为审核中',
    'Status pass'     => '通过',
    'Set status to pass'=> '设为通过',
    'Status refuse'   => '拒绝',
    'Set status to refuse'=> '设为拒绝',
    'Paytype'         => '支付方式',
    'Amount'          => '实付金额',
    'Paytime'         => '支付时间',
    'Transid'         => '流水号',
    'Refuse_remark'   => '拒绝理由',
    'Is_show'         => '上架状态',
    'Is_show normal'  => '上架',
    'Is_show hidden'  => '下架',
    'Distance'        => '距离',
    'Goods_car_id'    => '车型',
    'User.nickname'   => '昵称'
];
