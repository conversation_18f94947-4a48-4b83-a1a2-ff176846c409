define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'goods/index' + location.search,
                    add_url: 'goods/add',
                    edit_url: 'goods/edit',
                    del_url: 'goods/del',
                    multi_url: 'goods/multi',
                    import_url: 'goods/import',
                    table: 'goods',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                //切换卡片视图和表格视图两种模式
                showToggle: false,
                //显示隐藏列可以快速切换字段列的显示和隐藏
                showColumns: false,
                //导出整个表的所有行导出整个表的所有行
                showExport: false,
                //导出数据格式
                exportTypes: ['excel'],
                //搜索
                search: true,
                //表格上方的搜索搜索指表格上方的搜索
                searchFormVisible: false,
                //searchFormTemplate: 'customformtpl',
                //commonSearch:false,
                templateView: true,
                columns: [
                    [
                        { checkbox: true },
                        { field: 'id', title: __('Id') },
                        { field: 'user.nickname', title: __('User.nickname'), operate: 'LIKE' },
                        { field: 'car_type', title: __('Car_type'), searchList: { "all": __('All'), "pin": __('Pin') }, formatter: Table.api.formatter.normal },
                        { field: 'time_type', title: __('Time_type'), searchList: { "normal": __('Normal'), "danger": __('Danger') }, formatter: Table.api.formatter.normal },
                        { field: 'get_address_id', title: __('Get_address_id') },
                        { field: 'send_address_id', title: __('Send_address_id') },
                        {
                            field: 'get_date', title: __('Get_date'), operate: 'RANGE', addclass: 'datetimerange', autocomplete: false, formatter: function (value, row) {
                                return value + ' ' + row.get_datetime;
                            }
                        },
                        {
                            field: 'send_date', title: __('Send_date'), operate: 'RANGE', addclass: 'datetimerange', autocomplete: false, formatter: function (value, row) {
                                return value + ' ' + row.send_datetime;
                            }
                        },
                        { field: 'goods_type', title: __('Goods_type'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content },
                        { field: 'weight', title: __('Weight'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content },
                        { field: 'volume', title: __('Volume'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content },
                        { field: 'long', title: __('Long'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content },
                        { field: 'remark', title: __('Remark'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content },
                        { field: 'mobile', title: __('Mobile'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content },
                        { field: 'createtime', title: __('Createtime'), operate: 'RANGE', addclass: 'datetimerange', autocomplete: false, formatter: Table.api.formatter.datetime },
                        { field: 'status', title: __('Status'), searchList: { "pending": __('Status pending'), "audit": __('Status audit'), "pass": __('Status pass'), "refuse": __('Status refuse') }, formatter: Table.api.formatter.status },
                        { field: 'paytype', title: __('Paytype'), searchList: { "wechat": __('微信'), "alipay": __('支付宝') }, formatter: Table.api.formatter.status },
                        { field: 'amount', title: __('Amount'), operate: 'BETWEEN' },
                        { field: 'paytime', title: __('Paytime'), operate: 'RANGE', addclass: 'datetimerange', autocomplete: false, formatter: Table.api.formatter.datetime },
                        { field: 'is_show', title: __('Is_show'), searchList: { "normal": __('Is_show normal'), "hidden": __('Is_show hidden') }, formatter: Table.api.formatter.status },
                        { field: 'distance', title: __('Distance'), operate: 'BETWEEN' },
                        { field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate }
                    ]
                ]
            });

            Template.helper("cdnurl", function (image) {
                return Fast.api.cdnurl(image);
            });

            Template.helper("Moment", Moment);

            // 传递权限配置到模板
            Template.helper("Config", {
                operate: {
                    del: table.data('operate-del')
                }
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            // 绑定自定义事件
            Controller.api.bindCustomEvents();
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            },
            bindCustomEvents: function () {
                // 审核通过
                $(document).on('click', '.audit-pass', function() {
                    var id = $(this).data('id');
                    Layer.confirm('确定要审核通过这条货源信息吗？', function(index) {
                        Fast.api.ajax({
                            url: 'goods/auditPass',
                            data: {id: id}
                        }, function(data, ret) {
                            Layer.msg('审核通过成功');
                            $(".btn-refresh").trigger("click");
                            Layer.close(index);
                        });
                    });
                });

                // 审核拒绝
                $(document).on('click', '.audit-refuse', function() {
                    var id = $(this).data('id');
                    Layer.prompt({
                        title: '请输入拒绝理由',
                        formType: 2,
                        maxlength: 200,
                        area: ['400px', '200px']
                    }, function(value, index) {
                        if (!value || value.trim() === '') {
                            Layer.msg('请输入拒绝理由');
                            return false;
                        }
                        Fast.api.ajax({
                            url: 'goods/auditRefuse',
                            data: {id: id, refuse_remark: value}
                        }, function(data, ret) {
                            Layer.msg('审核拒绝成功');
                            $(".btn-refresh").trigger("click");
                            Layer.close(index);
                        });
                    });
                });

                // 上架/下架
                $(document).on('click', '.toggle-show', function() {
                    var id = $(this).data('id');
                    var currentShow = $(this).data('show');
                    var newShow = currentShow === 'normal' ? 'hidden' : 'normal';
                    var action = newShow === 'normal' ? '上架' : '下架';

                    Layer.confirm('确定要' + action + '这条货源信息吗？', function(index) {
                        Fast.api.ajax({
                            url: 'goods/toggleShow',
                            data: {id: id, is_show: newShow}
                        }, function(data, ret) {
                            Layer.msg(action + '成功');
                            $(".btn-refresh").trigger("click");
                            Layer.close(index);
                        });
                    });
                });

                // 详情查看
                $(document).on('click', '.detail', function() {
                    var id = $(this).data('id');
                    Fast.api.open('goods/detail?ids=' + id, '货源详情', {
                        area: ['90%', '90%']
                    });
                });

                // 删除货源
                $(document).on('click', '.delete-goods', function() {
                    var id = $(this).data('id');
                    Layer.confirm('确定要删除这条货源信息吗？删除后将无法恢复！', {
                        icon: 3,
                        title: '删除确认'
                    }, function(index) {
                        Fast.api.ajax({
                            url: 'goods/del',
                            data: {ids: id}
                        }, function(data, ret) {
                            Layer.msg('删除成功');
                            $(".btn-refresh").trigger("click");
                            Layer.close(index);
                        });
                    });
                });
            }
        }
    };
    return Controller;
});
