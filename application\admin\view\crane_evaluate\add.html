<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('吊车标题')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-crane_id" data-rule="required" data-source="crane/index" class="form-control selectpage" name="row[crane_id]" type="text" value="{$crane_id|default=''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Nickname')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-nickname" class="form-control" name="row[nickname]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Avatar')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-avatar" class="form-control" size="50" name="row[avatar]" type="text">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-avatar" class="btn btn-danger faupload" data-input-id="c-avatar" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-avatar"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-avatar" class="btn btn-primary fachoose" data-input-id="c-avatar" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-avatar"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-avatar"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Star')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-star" data-rule="required|between:1,5" class="form-control" name="row[star]" type="number" min="1" max="5" value="5" step="1">
            <span class="help-block">请输入1-5之间的整数，1星最低，5星最高</span>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Content')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-content" class="form-control " rows="5" name="row[content]" cols="50"></textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Images')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-images" class="form-control" size="50" name="row[images]" type="textarea">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-images" class="btn btn-danger faupload" data-input-id="c-images" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="true" data-preview-id="p-images"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-images" class="btn btn-primary fachoose" data-input-id="c-images" data-mimetype="image/*" data-multiple="true"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-images"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-images"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Weigh')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-weigh" data-rule="required" class="form-control" name="row[weigh]" type="number" value="0">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>

<script>
$(document).ready(function() {
    // 限制star输入框只能输入1-5的整数
    $('#c-star').on('input', function() {
        var value = parseInt($(this).val());

        // 如果输入的不是数字或者超出范围，则重置为5
        if (isNaN(value) || value < 1 || value > 5) {
            if ($(this).val() !== '') {
                $(this).val(5);
                Layer.msg('评分只能输入1-5之间的整数', {icon: 2});
            }
        }
    });

    // 防止输入小数点和负号
    $('#c-star').on('keypress', function(e) {
        // 只允许数字键
        if (e.which < 48 || e.which > 57) {
            // 允许退格键、删除键、Tab键、回车键
            if (e.which !== 8 && e.which !== 46 && e.which !== 9 && e.which !== 13) {
                e.preventDefault();
            }
        }
    });

    // 失去焦点时验证
    $('#c-star').on('blur', function() {
        var value = parseInt($(this).val());
        if (isNaN(value) || value < 1 || value > 5) {
            $(this).val(5);
            Layer.msg('评分已重置为5星，请输入1-5之间的整数', {icon: 1});
        }
    });
});
</script>
