define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'hire/index' + location.search,
                    add_url: 'hire/add',
                    edit_url: 'hire/edit',
                    del_url: 'hire/del',
                    multi_url: 'hire/multi',
                    import_url: 'hire/import',
                    table: 'hire',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                //切换卡片视图和表格视图两种模式
                showToggle: false,
                //显示隐藏列可以快速切换字段列的显示和隐藏
                showColumns: false,
                //导出整个表的所有行导出整个表的所有行
                showExport: false,
                //导出数据格式
                exportTypes: ['excel'],
                //搜索
                search: true,
                //表格上方的搜索搜索指表格上方的搜索
                searchFormVisible: false,
                templateView: true,
                columns: [
                    [
                        { checkbox: true },
                        { field: 'id', title: __('Id') },
                        { field: 'user.nickname', title: __('用户昵称'), operate: 'LIKE' },
                        { field: 'kind', title: __('Kind'), searchList: { "driver": __('Kind driver'), "repair": __('Kind repair') }, formatter: Table.api.formatter.normal },
                        { field: 'username', title: __('Username'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content },
                        { field: 'mobile', title: __('Mobile'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content },
                        { field: 'num', title: __('Num') },
                        { field: 'salary_range', title: __('Salary_range'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content },
                        { field: 'hire_car_id', title: __('Hire_car_id') },
                        { field: 'address', title: __('Address'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content },
                        { field: 'hire_post_ids', title: __('Hire_post_ids'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content },
                        { field: 'work_image', title: __('Work_image'), operate: false, events: Table.api.events.image, formatter: Table.api.formatter.image },
                        { field: 'car_image', title: __('Car_image'), operate: false, events: Table.api.events.image, formatter: Table.api.formatter.image },
                        { field: 'hire_experience_id', title: __('Experience'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content },
                        { field: 'hire_skills_ids', title: __('Skills'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content },
                        { field: 'createtime', title: __('Createtime'), operate: 'RANGE', addclass: 'datetimerange', autocomplete: false, formatter: Table.api.formatter.datetime },
                        { field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate }
                    ]
                ]
            });

            Template.helper("cdnurl", function (image) {
                return Fast.api.cdnurl(image);
            });

            Template.helper("Moment", Moment);

            // 为表格绑定事件
            Table.api.bindevent(table);

            // 绑定自定义模板中的删除按钮事件
            $(document).on('click', '.btn-delone', function () {
                var that = this;
                var id = $(this).data('id');
                Layer.confirm(__('Are you sure you want to delete this item?'), {
                    icon: 3,
                    title: __('Warning'),
                    shadeClose: true
                }, function (index) {
                    Fast.api.ajax({
                        url: 'hire/del',
                        data: {ids: id},
                        type: "POST"
                    }, function (data, ret) {
                        Layer.close(index);
                        Toastr.success(ret.msg);
                        table.bootstrapTable('refresh');
                    });
                });
            });
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
