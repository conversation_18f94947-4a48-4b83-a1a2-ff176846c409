define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'vip_order/index' + location.search,
                    add_url: 'vip_order/add',
                    edit_url: 'vip_order/edit',
                    del_url: 'vip_order/del',
                    multi_url: 'vip_order/multi',
                    import_url: 'vip_order/import',
                    table: 'vip_order',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                //切换卡片视图和表格视图两种模式
                showToggle: false,
                //显示隐藏列可以快速切换字段列的显示和隐藏
                showColumns: false,
                //导出整个表的所有行导出整个表的所有行
                showExport: false,
                //导出数据格式
                exportTypes: ['excel'],
                //搜索
                search: true,
                //表格上方的搜索搜索指表格上方的搜索
                searchFormVisible: false,
                templateView: true,
                columns: [
                    [
                        { checkbox: true },
                        { field: 'id', title: __('Id') },
                        { field: 'user.nickname', title: __('User.nickname'), operate: 'LIKE' },
                        { field: 'orderid', title: __('Orderid'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content },
                        { field: 'vip_id', title: __('Vip_id') },
                        { field: 'vip_name', title: __('Vip_name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content },
                        { field: 'vip_day', title: __('Vip_day') },
                        { field: 'createtime', title: __('Createtime'), operate: 'RANGE', addclass: 'datetimerange', autocomplete: false, formatter: Table.api.formatter.datetime },
                        { field: 'status', title: __('Status'), searchList: { "pending": __('Status pending'), "paid": __('Status paid') }, formatter: Table.api.formatter.status },
                        { field: 'paytype', title: __('Paytype'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content },
                        { field: 'vip_price', title: __('Vip_price'), operate: 'BETWEEN' },
                        { field: 'amount', title: __('Amount'), operate: 'BETWEEN' },
                        { field: 'paytime', title: __('Paytime'), operate: 'RANGE', addclass: 'datetimerange', autocomplete: false, formatter: Table.api.formatter.datetime },
                        { field: 'transid', title: __('Transid'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content },
                        { field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate }
                    ]
                ]
            });

            Template.helper("cdnurl", function (image) {
                return Fast.api.cdnurl(image);
            });

            Template.helper("Moment", Moment);

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
