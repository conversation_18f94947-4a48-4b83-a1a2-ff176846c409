<?php

namespace app\api\controller;

use app\common\controller\Api;
use fast\Pinyin;
use think\Db;

/**
 * 首页接口
 */
class Index extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    /**
     * 首页
     *
     */
    public function index()
    {
        $this->success('请求成功');
    }

    /**
     * 获取系统配置
     * @return void
     */
    public function getConfig()
    {
        $configName = input('configName');
        if (!$configName) $this->error('参数错误');
        $this->success('ok', config("site.$configName"));
    }


    /**
     * 获取协议
     *
     * @return void
     */
    public function agreement()
    {
        $agreementId = input('id');
        if (!$agreementId) {
            $this->error('配置参数错误');
        }
        $result = Db::name('agreement')->where('id', $agreementId)->find();
        $this->success('ok', $result);
    }

    /**
     * 获取轮播图列表
     *
     * @ApiMethod (GET)
     */
    public function bannerList()
    {
        $list = Db::name('banner')
            ->order('weigh desc, id desc')
            ->select();

        $this->success('获取成功', $list);
    }

    /**
     * 获取轮播图详情
     *
     * @ApiMethod (GET)
     * @ApiParams (name="id", type="int", required=true, description="轮播图ID")
     */
    public function bannerDetail()
    {
        $id = input('id');
        if (!$id) {
            $this->error('参数错误');
        }

        $banner = Db::name('banner')->where('id', $id)->find();
        if (!$banner) {
            $this->error('轮播图不存在');
        }

        $this->success('获取成功', $banner);
    }

    /**
     * 获取车型列表
     *
     * @ApiMethod (GET)
     */
    public function carTypeList()
    {
        $list = Db::name('car_type')
            ->order('weigh desc, id desc')
            ->select();

        $this->success('获取成功', $list);
    }

    /**
     * 获取用车备注列表
     *
     * @ApiMethod (GET)
     */
    public function goodsRemarkList()
    {
        $list = Db::name('goods_remark')
            ->order('weigh desc, id desc')
            ->select();

        $this->success('获取成功', $list);
    }

    /**
     * 获取经营范围列表
     *
     * @ApiMethod (GET)
     */
    public function rangeList()
    {
        $list = Db::name('range')
            ->order('weigh desc, id desc')
            ->select();

        $this->success('获取成功', $list);
    }

    /**
     * 获取VIP配置列表
     *
     * @ApiMethod (GET)
     */
    public function vipList()
    {
        $list = Db::name('vip')
            ->order('weigh desc, id desc')
            ->select();

        $this->success('获取成功', $list);
    }

    /**
     * 获取经营品牌列表
     *
     * @ApiMethod (GET)
     */
    public function brandList()
    {
        $list = Db::name('brand')
            ->order('weigh desc, id desc')
            ->select();

        // 按首字母分组
        $groupedList = [];
        $allBrands = []; // 存储所有品牌，用于"全部"选项

        foreach ($list as $brand) {
            $allBrands[] = $brand;

            // 获取品牌名称的首字母
            $firstChar = Pinyin::get(mb_substr($brand['name'], 0, 1, 'UTF-8'), true, ' ', true);
            if (!isset($groupedList[$firstChar])) {
                $groupedList[$firstChar] = [];
            }

            $groupedList[$firstChar][] = $brand;
        }

        // 对分组进行排序（A-Z）
        ksort($groupedList);

        // 构建新的数据格式：text:"A", data:{}
        $formattedGroups = [];
        foreach ($groupedList as $letter => $brands) {
            $formattedGroups[] = [
                'text' => $letter,
                'data' => $brands
            ];
        }

        // 构建最终返回数据
        $result = [
            'all' => $allBrands,  // 全部品牌
            'groups' => $formattedGroups  // 按字母分组的品牌，新格式
        ];

        $this->success('获取成功', $result);
    }

    /**
     * 获取轮胎品牌列表
     *
     * @ApiMethod (GET)
     */
    public function tireBrandList()
    {
        $list = Db::name('tire_brand')
            ->order('weigh desc, id desc')
            ->select();

        // 按首字母分组
        $groupedList = [];
        $allBrands = []; // 存储所有品牌，用于"全部"选项

        foreach ($list as $brand) {
            $allBrands[] = $brand;

            // 获取品牌名称的首字母
            $firstChar = Pinyin::get(mb_substr($brand['name'], 0, 1, 'UTF-8'), true, ' ', true);
            if (!isset($groupedList[$firstChar])) {
                $groupedList[$firstChar] = [];
            }

            $groupedList[$firstChar][] = $brand;
        }

        // 对分组进行排序（A-Z）
        ksort($groupedList);

        // 构建新的数据格式：text:"A", data:{}
        $formattedGroups = [];
        foreach ($groupedList as $letter => $brands) {
            $formattedGroups[] = [
                'text' => $letter,
                'data' => $brands
            ];
        }
        // 构建最终返回数据
        $result = [
            'all' => $allBrands,  // 全部品牌
            'groups' => $formattedGroups  // 按字母分组的品牌，新格式
        ];
        $this->success('获取成功', $result);
    }

    /**
     * 获取求助类型列表
     *
     * @ApiMethod (GET)
     */
    public function jianghuCategoryList()
    {
        $list = Db::name('jianghu_category')
            ->order('weigh desc, id desc')
            ->select();

        $this->success('获取成功', $list);
    }

    /**
     * 系统消息列表
     *
     * @ApiMethod (GET)
     * @ApiParams (name="page", type="int", required=false, description="页码，默认1")
     * @ApiParams (name="limit", type="int", required=false, description="每页数量，默认10")
     */
    public function noticeList()
    {
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);

        $list = Db::name('notice')
            ->order('createtime desc')
            ->page($page, $limit)
            ->select();

        // 如果用户已登录，标记哪些消息已读
        if ($this->auth->isLogin()) {
            $user = $this->auth->getUser();

            // 获取用户已读的消息ID列表
            $readNoticeIds = Db::name('user_notice_log')
                ->where('user_id', $user->id)
                ->column('notice_id');

            // 为每条消息添加已读状态
            foreach ($list as &$item) {
                $item['is_read'] = in_array($item['id'], $readNoticeIds);
            }
        } else {
            // 未登录用户，所有消息标记为未读
            foreach ($list as &$item) {
                $item['is_read'] = false;
            }
        }

        $this->success('获取成功', $list);
    }

    /**
     * 系统消息详情
     *
     * @ApiMethod (GET)
     * @ApiParams (name="id", type="int", required=true, description="消息ID")
     */
    public function noticeDetail()
    {
        $id = $this->request->get('id');
        if (!$id) {
            $this->error(__('Invalid parameters'));
        }

        $detail = Db::name('notice')->where('id', $id)->find();
        if (!$detail) {
            $this->error('消息不存在');
        }

        // 如果用户已登录，记录浏览记录
        if ($this->auth->isLogin()) {
            $user = $this->auth->getUser();

            // 检查是否已经有浏览记录
            $existLog = Db::name('user_notice_log')
                ->where(['user_id' => $user->id, 'notice_id' => $id])
                ->find();

            // 如果没有浏览记录，则添加
            if (!$existLog) {
                try {
                    Db::name('user_notice_log')->insert([
                        'user_id' => $user->id,
                        'notice_id' => $id,
                        'createtime' => time()
                    ]);
                } catch (\Exception $e) {
                    // 记录日志但不影响正常返回
                    \think\Log::error('添加用户消息浏览记录失败：' . $e->getMessage());
                }
            }

            $detail['is_read'] = true;
        } else {
            $detail['is_read'] = false;
        }

        $this->success('获取成功', $detail);
    }


    /**
     * 金刚区信息
     *
     * @ApiMethod (GET)
     */
    public function companyCategory()
    {
        $list = Db::name('company_category')
            ->order('weigh desc, id desc')
            ->select();

        $this->success('获取成功', $list);
    }

    /**
     * 获取修理车型列表
     *
     * @ApiMethod (GET)
     */
    public function repairCarList()
    {
        $list = Db::name('repair_car')
            ->order('weigh desc, id desc')
            ->select();

        $this->success('获取成功', $list);
    }

    /**
     * 获取修理范围列表
     *
     * @ApiMethod (GET)
     */
    public function repairRangeList()
    {
        $list = Db::name('repair_range')
            ->order('weigh desc, id desc')
            ->select();

        $this->success('获取成功', $list);
    }

    /**
     * 获取货物类型列表
     *
     * @ApiMethod (GET)
     */
    public function goodsCategoryList()
    {
        $list = Db::name('goods_category')
            ->order('weigh desc, id desc')
            ->select();

        $this->success('获取成功', $list);
    }

    /**
     * 获取二手车配置列表
     *
     * @ApiMethod (GET)
     */
    public function carConfigList()
    {
        // 定义所有配置类型
        $configTypes = ['fuel', 'supply', 'horsepower', 'engine', 'drive_mode', 'emission'];

        // 初始化结果数组
        $result = [];

        // 为每个类型初始化空数组
        foreach ($configTypes as $type) {
            $result[$type] = [];
        }

        // 查询所有配置数据
        $configList = Db::name('car_config')
            ->order('weigh desc, id desc')
            ->select();

        // 按status分组
        foreach ($configList as $config) {
            if (in_array($config['status'], $configTypes)) {
                $result[$config['status']][] = [
                    'id' => $config['id'],
                    'name' => $config['name'],
                    'weigh' => $config['weigh'],
                    'createtime' => $config['createtime']
                ];
            }
        }
        $this->success('获取成功', $result);
    }

    /**
     * 获取车型列表
     *
     * @ApiMethod (GET)
     */
    public function getHireCar()
    {
        $list = Db::name('hire_car')
            ->field('id, name')
            ->order('weigh desc, id asc')
            ->select();

        $this->success('获取成功', $list);
    }

    /**
     * 获取从业经验列表
     *
     * @ApiMethod (GET)
     */
    public function getHireExperience()
    {
        $list = Db::name('hire_experience')
            ->field('id, name')
            ->order('weigh desc, id asc')
            ->select();

        $this->success('获取成功', $list);
    }

    /**
     * 获取驾照类型列表
     *
     * @ApiMethod (GET)
     */
    public function getHireLicense()
    {
        $list = Db::name('hire_license')
            ->field('id, name')
            ->order('weigh desc, id asc')
            ->select();

        $this->success('获取成功', $list);
    }

    /**
     * 获取岗位福利列表
     *
     * @ApiMethod (GET)
     */
    public function getHirePost()
    {
        $list = Db::name('hire_post')
            ->field('id, name')
            ->order('weigh desc, id asc')
            ->select();

        $this->success('获取成功', $list);
    }

    /**
     * 获取所需技能列表
     *
     * @ApiMethod (GET)
     */
    public function getHireSkills()
    {
        $list = Db::name('hire_skills')
            ->field('id, name')
            ->order('weigh desc, id asc')
            ->select();

        $this->success('获取成功', $list);
    }

    /**
     * 获取所有招聘相关配置
     *
     * @ApiMethod (GET)
     */
    public function getHireConfig()
    {
        $config = [
            'car' => Db::name('hire_car')
                ->field('id, name')
                ->order('weigh desc, id asc')
                ->select(),
            'experience' => Db::name('hire_experience')
                ->field('id, name')
                ->order('weigh desc, id asc')
                ->select(),
            'license' => Db::name('hire_license')
                ->field('id, name')
                ->order('weigh desc, id asc')
                ->select(),
            'post' => Db::name('hire_post')
                ->field('id, name')
                ->order('weigh desc, id asc')
                ->select(),
            'skills' => Db::name('hire_skills')
                ->field('id, name')
                ->order('weigh desc, id asc')
                ->select()
        ];

        $this->success('获取成功', $config);
    }
}
