<div class="panel panel-default panel-intro">

    <div class="panel-heading">
        {:build_heading(null,FALSE)}
        <ul class="nav nav-tabs" data-field="kind">
            <li class="{:$Think.get.kind === null ? 'active' : ''}"><a href="#t-all" data-value=""
                    data-toggle="tab">{:__('All')}</a></li>
            {foreach name="kindList" item="vo"}
            <li class="{:$Think.get.kind === (string)$key ? 'active' : ''}"><a href="#t-{$key}" data-value="{$key}"
                    data-toggle="tab">{$vo}</a></li>
            {/foreach}
        </ul>
    </div>

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}"><i
                                class="fa fa-refresh"></i> </a>
                        <!-- <a href="javascript:;" class="btn btn-danger btn-del btn-disabled disabled {:$auth->check('hire/del')?'':'hide'}" title="{:__('Delete')}" ><i class="fa fa-trash"></i> {:__('Delete')}</a> -->
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                        data-operate-edit="{:$auth->check('hire/edit')}"
                        data-operate-del="{:$auth->check('hire/del')}" width="100%">
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script type="text/html" id="itemtpl">
        <% if(i == 0){ %>
            <div class="order-head">
                <div class="col-sm-3 text-center obr flex">招聘信息</div>
                <div class="col-sm-2 text-center obr">联系信息</div>
                <div class="col-sm-2 text-center obr">职位要求</div>
                <div class="col-sm-2 text-center obr">工作地点</div>
                <div class="col-sm-2 text-center obr">发布时间</div>
                <div class="col-sm-1 text-center">操作</div>
            </div>
        <% } %>

        <div class="col-sm-12 mt-15">
            <div class="item-top">
                <span>ID：<%=item.id%></span>
                <span style="margin-left:100px;">发布用户：<%=item.user ? item.user.nickname : '未知用户'%></span>
                <span style="margin-left:100px;">发布时间：<%=Moment(item.createtime*1000).format("YYYY-MM-DD HH:mm:ss")%></span>
            </div>
            <div class="item-content">
                <!-- 招聘信息 col-sm-3 -->
                <div class="col-sm-3 vhc obr" style="padding: 10px;">
                    <div class="hire-info">
                        <div class="hire-type">
                            <strong><%=item.kind_text%></strong>
                            <% if(item.post_text && item.kind_text == '修理工'){ %>
                            <br/><span class="label label-info"><%=item.post_text%></span>
                            <% } %>
                        </div>
                        <div class="hire-details" style="margin-top: 5px; font-size: 12px;">
                            <div>招聘人数：<%=item.num%>人</div>
                            <% if(item.salary_range){ %><div>薪资范围：<%=item.salary_range%></div><% } %>
                            <% if(item.hire_car_name){ %><div class="text-info">车型：<%=item.hire_car_name%></div><% } %>
                        </div>
                    </div>
                </div>

                <!-- 联系信息 col-sm-2 -->
                <div class="col-sm-2 vhc obr flex">
                    <div class="contact-info">
                        <div><strong><%=item.username || '未设置'%></strong></div>
                        <% if(item.mobile){ %>
                        <div class="text-info"><i class="fa fa-phone"></i> <%=item.mobile%></div>
                        <% } %>
                    </div>
                </div>

                <!-- 职位要求 col-sm-2 -->
                <div class="col-sm-2 vhc obr flex">
                    <div class="requirement-info">
                        <% if(item.hire_license_name){ %>
                        <div><strong>驾照：<%=item.hire_license_name%></strong></div>
                        <% } %>
                        <% if(item.hire_experience_name){ %>
                        <div>经验：<%=item.hire_experience_name%></div>
                        <% } %>
                        <% if(item.hire_post_names){ %>
                        <div class="text-success">福利：<%=item.hire_post_names%></div>
                        <% } %>
                        <% if(item.hire_skills_names){ %>
                        <div class="text-muted">技能：<%=item.hire_skills_names%></div>
                        <% } %>
                    </div>
                </div>

                <!-- 工作地点 col-sm-2 -->
                <div class="col-sm-2 vhc obr flex">
                    <div class="location-info text-center">
                        <div><strong><%=item.address || '未设置'%></strong></div>
                        <% if(item.province && item.city){ %>
                        <div class="text-muted"><%=item.province%> <%=item.city%></div>
                        <% } %>
                    </div>
                </div>

                <!-- 发布时间 col-sm-2 -->
                <div class="col-sm-2 vhc obr flex">
                    <div class="time-info text-center">
                        <span class="label label-primary">
                            <%=Moment(item.createtime*1000).format("YYYY-MM-DD HH:mm")%>
                        </span>
                    </div>
                </div>

                <!-- 操作按钮 col-sm-1 -->
                <div class="col-sm-1 vhc flex">
                    <div class="operate text-center">
                        {:$auth->check('hire/del') ? '<a href="javascript:;" class="btn btn-xs btn-danger btn-delone" data-id="<%=item.id%>" title="删除"><i class="fa fa-trash"></i> 删除</a>' : ''}
                    </div>
                </div>
            </div>
            <% if(item.content){ %> 
            <div class="item-remark">
                <small class="text-muted"><i class="fa fa-file-text-o"></i> 职位详情：<%=item.content%></small>
            </div>
            <% } %>
        </div>
    </script>

    <style>
        .flex {
            display: flex;
            flex-wrap: wrap;
        }

        .mt-15 {
            margin-top: 15px;
        }

        .item-goods {
            border-bottom: solid 1px #e6e6e6;
            padding: 15px;
        }

        .item-goods:last-child {
            border: none;
        }

        .ctr {
            text-align: right;
            line-height: 35px;
            font-weight: bold;
        }

        .vhc {
            padding: 15px;
            height: 100%;
            align-items: center;
            justify-content: center;
        }

        .order-head {
            background: #f7f7f7;
            padding: 0px 15px;
            width: 100%;
            font-weight: bold;
            margin-top: 5px;
        }

        .order-head div {
            padding: 15px;
        }

        .item-top {
            background: #f7f7f7;
            padding: 10px;
            width: 100%;
            border: 1px solid #e6e6e6;
        }

        .item-content {
            border: 1px solid #e6e6e6;
            border-top: none;
        }

        .item-remark {
            background: #f9f9f9;
            padding: 10px;
            border: 1px solid #e6e6e6;
            border-top: none;
        }

        .obr {
            border-right: 1px solid #e6e6e6;
        }

        .operate .btn {
            margin: 2px;
        }

        .operate .btn-xs {
            padding: 3px 6px;
            font-size: 11px;
        }

        /*.fixed-table-toolbar{display: none;}*/
    </style>

</div>
