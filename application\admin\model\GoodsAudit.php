<?php

namespace app\admin\model;

use think\Model;


class GoodsAudit extends Model
{





    // 表名
    protected $name = 'goods_audit';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'kind_text',
        'status_text'
    ];



    public function getKindList()
    {
        return ['user' => __('Kind user'), 'company' => __('Kind company')];
    }

    public function getStatusList()
    {
        return ['pending' => __('Status pending'), 'pass' => __('Status pass'), 'refuse' => __('Status refuse')];
    }


    public function getKindTextAttr($value, $data)
    {
        $value = $value ?: ($data['kind'] ?? '');
        $list = $this->getKindList();
        return $list[$value] ?? '';
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ?: ($data['status'] ?? '');
        $list = $this->getStatusList();
        return $list[$value] ?? '';
    }


    public function user()
    {
        return $this->belongsTo('User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
