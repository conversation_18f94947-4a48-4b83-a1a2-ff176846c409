<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use think\Db;

class Index extends Frontend
{

    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';
    protected $layout = '';

    public function index()
    {
        $this->assign('app_download_qrcode', config('site.app_download_qrcode'));
        $this->assign('customer_mobile', config('site.customer_mobile'));
        $this->assign('customer_email', config('site.customer_email'));
        $this->assign('customer_company', config('site.customer_company'));
        return $this->view->fetch();
    }

    /**
     * 协议配置html
     * @param $agreementId
     * @return string
     * @throws \think\Exception
     */
    public function agreement()
    {
        $agreementId = input('id');
        if (!$agreementId) {
            $this->error('配置参数错误');
        }
        $result = Db::name('agreement')->where('id', $agreementId)->find();
        $this->view->assign('agreement', $result);
        return $this->view->fetch();
    }
}
