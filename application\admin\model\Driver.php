<?php

namespace app\admin\model;

use think\Model;


class Driver extends Model
{

    

    

    // 表名
    protected $name = 'driver';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'car_text',
        'card_text',
        'driving_text',
        'license_text',
        'work_text',
        'all_audit_text'
    ];
    

    







    /**
     * 获取认证状态文本
     */
    public function getCarTextAttr($value, $data)
    {
        return $this->getStatusText($data['car'] ?? 0);
    }

    public function getCardTextAttr($value, $data)
    {
        return $this->getStatusText($data['card'] ?? 0);
    }

    public function getDrivingTextAttr($value, $data)
    {
        return $this->getStatusText($data['driving'] ?? 0);
    }

    public function getLicenseTextAttr($value, $data)
    {
        return $this->getStatusText($data['license'] ?? 0);
    }

    public function getWorkTextAttr($value, $data)
    {
        return $this->getStatusText($data['work'] ?? 0);
    }

    public function getAllAuditTextAttr($value, $data)
    {
        $status = $data['all_audit'] ?? 0;
        return $status == 1 ? '全部认证' : '未全部认证';
    }

    /**
     * 获取状态文本
     * @param int $status 状态值
     * @return string
     */
    private function getStatusText($status)
    {
        $statusMap = [
            0 => '未上传',
            1 => '待审核',
            2 => '已通过',
            3 => '已拒绝'
        ];
        return $statusMap[$status] ?? '未知状态';
    }

    /**
     * 更新认证状态
     * @param int $userId 用户ID
     * @param string $type 认证类型 car|card|driving|license|work
     * @param int $status 状态 0=未上传 1=待审核 2=已通过 3=已拒绝
     */
    public static function updateAuthStatus($userId, $type, $status)
    {
        // 获取或创建司机认证记录
        $driver = self::where('user_id', $userId)->find();
        if (!$driver) {
            $driver = new self();
            $driver->user_id = $userId;
            $driver->car = 0;
            $driver->card = 0;
            $driver->driving = 0;
            $driver->license = 0;
            $driver->work = 0;
            $driver->all_audit = 0;
            $driver->createtime = time();
        }

        // 更新对应的认证状态
        $driver->$type = $status;

        // 检查是否全部认证通过（所有项都是2=已通过）
        $allPassed = ($driver->car == 2 && $driver->card == 2 && $driver->driving == 2 && $driver->license == 2 && $driver->work == 2);
        $driver->all_audit = $allPassed ? 1 : 0;

        $driver->save();

        return $driver;
    }

    /**
     * 获取用户认证状态
     * @param int $userId 用户ID
     * @return array
     */
    public static function getUserAuthStatus($userId)
    {
        $driver = self::where('user_id', $userId)->find();
        if (!$driver) {
            return [
                'car' => 0,
                'card' => 0,
                'driving' => 0,
                'license' => 0,
                'work' => 0,
                'all_audit' => 0,
                'car_text' => '未上传',
                'card_text' => '未上传',
                'driving_text' => '未上传',
                'license_text' => '未上传',
                'work_text' => '未上传',
                'all_audit_text' => '未全部认证'
            ];
        }

        return [
            'car' => $driver->car,
            'card' => $driver->card,
            'driving' => $driver->driving,
            'license' => $driver->license,
            'work' => $driver->work,
            'all_audit' => $driver->all_audit,
            'car_text' => $driver->car_text,
            'card_text' => $driver->card_text,
            'driving_text' => $driver->driving_text,
            'license_text' => $driver->license_text,
            'work_text' => $driver->work_text,
            'all_audit_text' => $driver->all_audit_text
        ];
    }

    public function user()
    {
        return $this->belongsTo('User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
