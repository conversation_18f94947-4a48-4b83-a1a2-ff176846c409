<?php

namespace app\api\controller;

use app\common\controller\Api;
use think\Db;

/**
 * 司机认证接口
 */
class Driver extends Api
{
    protected $noNeedLogin = [];
    protected $noNeedRight = '*';

    /**
     * 身份证认证申请
     *
     * @ApiMethod (POST)
     * @ApiParams (name="realname", type="string", required=true, description="真实姓名")
     * @ApiParams (name="idcard", type="string", required=true, description="身份证号")
     * @ApiParams (name="font_image", type="string", required=true, description="身份证正面")
     * @ApiParams (name="reverse_image", type="string", required=true, description="身份证反面")
     */
    public function cardApply()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        // 检查是否已有申请记录
        $existing = Db::name('driver_card')->where('user_id', $user->id)->find();
        if ($existing && $existing['status'] == 'pending') {
            $this->error('您已有审核中的申请，请等待审核结果');
        }
        if ($existing && $existing['status'] == 'pass') {
            $this->error('您已通过身份证认证，无需重复申请');
        }   

        $realname = $this->request->post('realname');
        $idcard = $this->request->post('idcard');
        $font_image = $this->request->post('font_image');
        $reverse_image = $this->request->post('reverse_image');

        // 验证必填参数
        if (!$realname || !$idcard || !$font_image || !$reverse_image) {
            $this->error(__('Invalid parameters'));
        }

        // 验证身份证号格式
        if (!preg_match('/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/', $idcard)) {
            $this->error('身份证号格式不正确');
        }

        $data = [
            'user_id' => $user->id,
            'realname' => $realname,
            'idcard' => $idcard,
            'font_image' => $font_image,
            'reverse_image' => $reverse_image,
            'status' => 'pending',
            'createtime' => time()
        ];

        try {
            if ($existing && $existing['status'] == 'refuse') {
                // 如果之前被拒绝，更新记录
                Db::name('driver_card')->where('id', $existing['id'])->update($data);
            } else {
                // 新增记录
                Db::name('driver_card')->insert($data);
            }

            // 同步更新司机认证状态表（提交申请时设为待审核状态）
            \app\admin\model\Driver::updateAuthStatus($user->id, 'card', 1);
        } catch (\Exception $e) {
            $this->error('申请提交失败，请重试');
        }

        $this->success('身份证认证申请提交成功，请等待审核');
    }

    /**
     * 身份证认证详情
     *
     * @ApiMethod (GET)
     */
    public function cardInfo()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $card = Db::name('driver_card')->where('user_id', $user->id)->find();
        if (!$card) {
            $this->success('获取成功', ['has_apply' => false]);
        }

        $this->success('获取成功', [
            'has_apply' => true,
            'card' => $card
        ]);
    }

    /**
     * 身份证认证编辑
     *
     * @ApiMethod (POST)
     * @ApiParams (name="realname", type="string", required=true, description="真实姓名")
     * @ApiParams (name="idcard", type="string", required=true, description="身份证号")
     * @ApiParams (name="font_image", type="string", required=true, description="身份证正面")
     * @ApiParams (name="reverse_image", type="string", required=true, description="身份证反面")
     */
    public function cardEdit()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        // 检查申请记录
        $card = Db::name('driver_card')->where('user_id', $user->id)->find();
        if (!$card) {
            $this->error('您还没有身份证认证记录，请先申请认证');
        }

        // 只有审核拒绝的记录才能编辑
        if ($card['status'] != 'refuse') {
            $this->error('只有审核拒绝的申请才能编辑');
        }

        $realname = $this->request->post('realname');
        $idcard = $this->request->post('idcard');
        $font_image = $this->request->post('font_image');
        $reverse_image = $this->request->post('reverse_image');

        // 验证必填参数
        if (!$realname || !$idcard || !$font_image || !$reverse_image) {
            $this->error(__('Invalid parameters'));
        }

        // 验证身份证号格式
        if (!preg_match('/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/', $idcard)) {
            $this->error('身份证号格式不正确');
        }

        $data = [
            'realname' => $realname,
            'idcard' => $idcard,
            'font_image' => $font_image,
            'reverse_image' => $reverse_image,
            'status' => 'pending',
            'createtime' => time()
        ];

        try {
            Db::name('driver_card')->where('id', $card['id'])->update($data);

            // 同步更新司机认证状态表（重新提交时设为待审核状态）
            \app\admin\model\Driver::updateAuthStatus($user->id, 'card', 1);
        } catch (\Exception $e) {
            $this->error('修改失败，请重试');
        }

        $this->success('身份证认证信息修改成功，请等待重新审核');
    }

    /**
     * 驾驶证认证申请
     *
     * @ApiMethod (POST)
     * @ApiParams (name="font_image", type="string", required=true, description="驾驶证正面")
     * @ApiParams (name="reverse_image", type="string", required=true, description="驾驶证反面")
     */
    public function licenseApply()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        // 检查是否已有申请记录
        $existing = Db::name('driver_license')->where('user_id', $user->id)->find();
        if ($existing && $existing['status'] == 'pending') {
            $this->error('您已有审核中的申请，请等待审核结果');
        }
        if ($existing && $existing['status'] == 'pass') {
            $this->error('您已通过驾驶证认证，无需重复申请');
        }

        $font_image = $this->request->post('font_image');
        $reverse_image = $this->request->post('reverse_image');

        // 验证必填参数
        if (!$font_image || !$reverse_image) {
            $this->error(__('Invalid parameters'));
        }

        $data = [
            'user_id' => $user->id,
            'font_image' => $font_image,
            'reverse_image' => $reverse_image,
            'status' => 'pending',
            'createtime' => time()
        ];

        try {
            if ($existing && $existing['status'] == 'refuse') {
                // 如果之前被拒绝，更新记录
                Db::name('driver_license')->where('id', $existing['id'])->update($data);
            } else {
                // 新增记录
                Db::name('driver_license')->insert($data);
            }

            // 同步更新司机认证状态表
            \app\admin\model\Driver::updateAuthStatus($user->id, 'license', 1);
        } catch (\Exception $e) {
            $this->error('申请提交失败，请重试');
        }

        $this->success('驾驶证认证申请提交成功，请等待审核');
    }

    /**
     * 驾驶证认证详情
     *
     * @ApiMethod (GET)
     */
    public function licenseInfo()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $license = Db::name('driver_license')->where('user_id', $user->id)->find();
        if (!$license) {
            $this->success('获取成功', ['has_apply' => false]);
        }

        $this->success('获取成功', [
            'has_apply' => true,
            'license' => $license
        ]);
    }

    /**
     * 驾驶证认证编辑
     *
     * @ApiMethod (POST)
     * @ApiParams (name="font_image", type="string", required=true, description="驾驶证正面")
     * @ApiParams (name="reverse_image", type="string", required=true, description="驾驶证反面")
     */
    public function licenseEdit()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        // 检查申请记录
        $license = Db::name('driver_license')->where('user_id', $user->id)->find();
        if (!$license) {
            $this->error('您还没有驾驶证认证记录，请先申请认证');
        }

        // 只有审核拒绝的记录才能编辑
        if ($license['status'] != 'refuse') {
            $this->error('只有审核拒绝的申请才能编辑');
        }

        $font_image = $this->request->post('font_image');
        $reverse_image = $this->request->post('reverse_image');

        // 验证必填参数
        if (!$font_image || !$reverse_image) {
            $this->error(__('Invalid parameters'));
        }

        $data = [
            'font_image' => $font_image,
            'reverse_image' => $reverse_image,
            'status' => 'pending',
            'createtime' => time()
        ];

        try {
            Db::name('driver_license')->where('id', $license['id'])->update($data);

            // 同步更新司机认证状态表
            \app\admin\model\Driver::updateAuthStatus($user->id, 'license', 1);
        } catch (\Exception $e) {
            $this->error('修改失败，请重试');
        }

        $this->success('驾驶证认证信息修改成功，请等待重新审核');
    }

    /**
     * 行驶证认证申请
     *
     * @ApiMethod (POST)
     * @ApiParams (name="font_image", type="string", required=true, description="行驶证正面")
     * @ApiParams (name="reverse_image", type="string", required=true, description="行驶证反面")
     * @ApiParams (name="number", type="string", required=true, description="车牌号")
     * @ApiParams (name="car_type", type="string", required=true, description="车辆类型")
     */
    public function drivingApply()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        // 检查是否已有申请记录
        $existing = Db::name('driver_driving')->where('user_id', $user->id)->find();
        if ($existing && $existing['status'] == 'pending') {
            $this->error('您已有审核中的申请，请等待审核结果');
        }
        if ($existing && $existing['status'] == 'pass') {
            $this->error('您已通过行驶证认证，无需重复申请');
        }

        $font_image = $this->request->post('font_image');
        $reverse_image = $this->request->post('reverse_image');
        $number = $this->request->post('number');
        $car_type = $this->request->post('car_type');

        // 验证必填参数
        if (!$font_image || !$reverse_image || !$number || !$car_type) {
            $this->error(__('Invalid parameters'));
        }

        $data = [
            'user_id' => $user->id,
            'font_image' => $font_image,
            'reverse_image' => $reverse_image,
            'number' => $number,
            'car_type' => $car_type,
            'status' => 'pending',
            'createtime' => time()
        ];

        try {
            if ($existing && $existing['status'] == 'refuse') {
                // 如果之前被拒绝，更新记录
                Db::name('driver_driving')->where('id', $existing['id'])->update($data);
            } else {
                // 新增记录
                Db::name('driver_driving')->insert($data);
            }

            // 同步更新司机认证状态表
            \app\admin\model\Driver::updateAuthStatus($user->id, 'driving', 1);
        } catch (\Exception $e) {
            $this->error('申请提交失败，请重试');
        }

        $this->success('行驶证认证申请提交成功，请等待审核');
    }

    /**
     * 行驶证认证详情
     *
     * @ApiMethod (GET)
     */
    public function drivingInfo()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $driving = Db::name('driver_driving')->where('user_id', $user->id)->find();
        if (!$driving) {
            $this->success('获取成功', ['has_apply' => false]);
        }

        $this->success('获取成功', [
            'has_apply' => true,
            'driving' => $driving
        ]);
    }

    /**
     * 行驶证认证编辑
     *
     * @ApiMethod (POST)
     * @ApiParams (name="font_image", type="string", required=true, description="行驶证正面")
     * @ApiParams (name="reverse_image", type="string", required=true, description="行驶证反面")
     * @ApiParams (name="number", type="string", required=true, description="车牌号")
     * @ApiParams (name="car_type", type="string", required=true, description="车辆类型")
     */
    public function drivingEdit()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        // 检查申请记录
        $driving = Db::name('driver_driving')->where('user_id', $user->id)->find();
        if (!$driving) {
            $this->error('您还没有行驶证认证记录，请先申请认证');
        }

        // 只有审核拒绝的记录才能编辑
        if ($driving['status'] != 'refuse') {
            $this->error('只有审核拒绝的申请才能编辑');
        }

        $font_image = $this->request->post('font_image');
        $reverse_image = $this->request->post('reverse_image');
        $number = $this->request->post('number');
        $car_type = $this->request->post('car_type');

        // 验证必填参数
        if (!$font_image || !$reverse_image || !$number || !$car_type) {
            $this->error(__('Invalid parameters'));
        }

        $data = [
            'font_image' => $font_image,
            'reverse_image' => $reverse_image,
            'number' => $number,
            'car_type' => $car_type,
            'status' => 'pending',
            'createtime' => time()
        ];

        try {
            Db::name('driver_driving')->where('id', $driving['id'])->update($data);

            // 同步更新司机认证状态表
            \app\admin\model\Driver::updateAuthStatus($user->id, 'driving', 1);
        } catch (\Exception $e) {
            $this->error('修改失败，请重试');
        }

        $this->success('行驶证认证信息修改成功，请等待重新审核');
    }

    /**
     * 从业资格证认证申请
     *
     * @ApiMethod (POST)
     * @ApiParams (name="image", type="string", required=true, description="从业资格证")
     */
    public function workApply()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        // 检查是否已有申请记录
        $existing = Db::name('driver_work')->where('user_id', $user->id)->find();
        if ($existing && $existing['status'] == 'pending') {
            $this->error('您已有审核中的申请，请等待审核结果');
        }
        if ($existing && $existing['status'] == 'pass') {
            $this->error('您已通过从业资格证认证，无需重复申请');
        }

        $image = $this->request->post('image');

        // 验证必填参数
        if (!$image) {
            $this->error(__('Invalid parameters'));
        }

        $data = [
            'user_id' => $user->id,
            'image' => $image,
            'status' => 'pending',
            'createtime' => time()
        ];

        try {
            if ($existing && $existing['status'] == 'refuse') {
                // 如果之前被拒绝，更新记录
                Db::name('driver_work')->where('id', $existing['id'])->update($data);
            } else {
                // 新增记录
                Db::name('driver_work')->insert($data);
            }

            // 同步更新司机认证状态表
            \app\admin\model\Driver::updateAuthStatus($user->id, 'work', 1);
        } catch (\Exception $e) {
            $this->error('申请提交失败，请重试');
        }

        $this->success('从业资格证认证申请提交成功，请等待审核');
    }

    /**
     * 从业资格证认证详情
     *
     * @ApiMethod (GET)
     */
    public function workInfo()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $work = Db::name('driver_work')->where('user_id', $user->id)->find();
        if (!$work) {
            $this->success('获取成功', ['has_apply' => false]);
        }

        $this->success('获取成功', [
            'has_apply' => true,
            'work' => $work
        ]);
    }

    /**
     * 从业资格证认证编辑
     *
     * @ApiMethod (POST)
     * @ApiParams (name="image", type="string", required=true, description="从业资格证")
     */
    public function workEdit()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        // 检查申请记录
        $work = Db::name('driver_work')->where('user_id', $user->id)->find();
        if (!$work) {
            $this->error('您还没有从业资格证认证记录，请先申请认证');
        }

        // 只有审核拒绝的记录才能编辑
        if ($work['status'] != 'refuse') {
            $this->error('只有审核拒绝的申请才能编辑');
        }

        $image = $this->request->post('image');

        // 验证必填参数
        if (!$image) {
            $this->error(__('Invalid parameters'));
        }

        $data = [
            'image' => $image,
            'status' => 'pending',
            'createtime' => time()
        ];

        try {
            Db::name('driver_work')->where('id', $work['id'])->update($data);

            // 同步更新司机认证状态表
            \app\admin\model\Driver::updateAuthStatus($user->id, 'work', 1);
        } catch (\Exception $e) {
            $this->error('修改失败，请重试');
        }

        $this->success('从业资格证认证信息修改成功，请等待重新审核');
    }

    /**
     * 人车合影认证申请
     *
     * @ApiMethod (POST)
     * @ApiParams (name="font_image", type="string", required=true, description="车辆正面照")
     * @ApiParams (name="reverse_image", type="string", required=true, description="人车合影照片")
     */
    public function carApply()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        // 检查是否已有申请记录
        $existing = Db::name('driver_car')->where('user_id', $user->id)->find();
        if ($existing && $existing['status'] == 'pending') {
            $this->error('您已有审核中的申请，请等待审核结果');
        }
        if ($existing && $existing['status'] == 'pass') {
            $this->error('您已通过人车合影认证，无需重复申请');
        }

        $font_image = $this->request->post('font_image');
        $reverse_image = $this->request->post('reverse_image');

        // 验证必填参数
        if (!$font_image || !$reverse_image) {
            $this->error(__('Invalid parameters'));
        }

        $data = [
            'user_id' => $user->id,
            'font_image' => $font_image,
            'reverse_image' => $reverse_image,
            'status' => 'pending',
            'createtime' => time()
        ];

        try {
            if ($existing && $existing['status'] == 'refuse') {
                // 如果之前被拒绝，更新记录
                Db::name('driver_car')->where('id', $existing['id'])->update($data);
            } else {
                // 新增记录
                Db::name('driver_car')->insert($data);
            }

            // 同步更新司机认证状态表
            \app\admin\model\Driver::updateAuthStatus($user->id, 'car', 1);
        } catch (\Exception $e) {
            $this->error('申请提交失败，请重试');
        }

        $this->success('人车合影认证申请提交成功，请等待审核');
    }

    /**
     * 人车合影认证详情
     *
     * @ApiMethod (GET)
     */
    public function carInfo()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $car = Db::name('driver_car')->where('user_id', $user->id)->find();
        if (!$car) {
            $this->success('获取成功', ['has_apply' => false]);
        }

        $this->success('获取成功', [
            'has_apply' => true,
            'car' => $car
        ]);
    }

    /**
     * 人车合影认证编辑
     *
     * @ApiMethod (POST)
     * @ApiParams (name="font_image", type="string", required=true, description="车辆正面照")
     * @ApiParams (name="reverse_image", type="string", required=true, description="人车合影照片")
     */
    public function carEdit()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        // 检查申请记录
        $car = Db::name('driver_car')->where('user_id', $user->id)->find();
        if (!$car) {
            $this->error('您还没有人车合影认证记录，请先申请认证');
        }

        // 只有审核拒绝的记录才能编辑
        if ($car['status'] != 'refuse') {
            $this->error('只有审核拒绝的申请才能编辑');
        }

        $font_image = $this->request->post('font_image');
        $reverse_image = $this->request->post('reverse_image');

        // 验证必填参数
        if (!$font_image || !$reverse_image) {
            $this->error(__('Invalid parameters'));
        }

        $data = [
            'font_image' => $font_image,
            'reverse_image' => $reverse_image,
            'status' => 'pending',
            'createtime' => time()
        ];

        try {
            Db::name('driver_car')->where('id', $car['id'])->update($data);

            // 同步更新司机认证状态表
            \app\admin\model\Driver::updateAuthStatus($user->id, 'car', 1);
        } catch (\Exception $e) {
            $this->error('修改失败，请重试');
        }

        $this->success('人车合影认证信息修改成功，请等待重新审核');
    }

    /**
     * 获取所有认证状态
     *
     * @ApiMethod (GET)
     */
    public function allStatus()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        // 从司机认证状态表获取统一的认证状态
        $authStatus = \app\admin\model\Driver::getUserAuthStatus($user->id);

        // 获取详细的认证信息（用于显示拒绝原因等）
        $card = Db::name('driver_card')->where('user_id', $user->id)->find();
        $license = Db::name('driver_license')->where('user_id', $user->id)->find();
        $driving = Db::name('driver_driving')->where('user_id', $user->id)->find();
        $work = Db::name('driver_work')->where('user_id', $user->id)->find();
        $car = Db::name('driver_car')->where('user_id', $user->id)->find();

        $this->success('获取成功', [
            'auth_status' => $authStatus,
            'details' => [
                'card' => $card ? $card : ['status' => 'none'],
                'license' => $license ? $license : ['status' => 'none'],
                'driving' => $driving ? $driving : ['status' => 'none'],
                'work' => $work ? $work : ['status' => 'none'],
                'car' => $car ? $car : ['status' => 'none']
            ]
        ]);
    }

    /**
     * 获取认证状态概览（新接口）
     *
     * @ApiMethod (GET)
     */
    public function authStatus()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        // 从司机认证状态表获取统一的认证状态
        $authStatus = \app\admin\model\Driver::getUserAuthStatus($user->id);

        $this->success('获取成功', $authStatus);
    }
}
