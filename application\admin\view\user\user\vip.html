<form id="vip-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    {:token()}
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('用户昵称')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-rule="required" class="form-control selectpage" data-field="nickname" data-source="user/user/index" name="user_id" type="text" value="{$user_id|default=''}" disabled>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('当前VIP状态')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="form-control-static">
                {if condition="$user.is_vip eq 1"}
                    <span class="label label-success">VIP用户</span>
                    {if condition="$user.endtime gt 0"}
                        <br><small>到期时间: {$user.endtime|date="Y-m-d H:i:s",###}</small>
                    {/if}
                {else/}
                    <span class="label label-default">普通用户</span>
                {/if}
            </div>
        </div>
    </div>
   <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Gender')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[action]', ['add'=>__('添加VIP天数'), 'set'=>__('设置到期时间'), 'remove'=>__('移除VIP')] )}
        </div>
    </div>
    <div class="form-group" data-favisible="action=add">
        <label class="control-label col-xs-12 col-sm-2">{:__('VIP天数')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-days" data-rule="required;integer;min(1)" class="form-control" name="row[days]" type="number" value="30" min="1" placeholder="{:__('Please enter the number of days')}">
        </div>
    </div>
    <div class="form-group" data-favisible="action=set">
        <label class="control-label col-xs-12 col-sm-2">{:__('新的到期时间')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-endtime" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[endtime]" type="text" value="" placeholder="{:__('设置到期时间')}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>

