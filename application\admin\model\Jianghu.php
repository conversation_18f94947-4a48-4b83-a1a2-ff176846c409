<?php

namespace app\admin\model;

use think\Model;


class <PERSON><PERSON> extends Model
{

    

    

    // 表名
    protected $name = 'jiang<PERSON>';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'kind_text'
    ];
    

    
    public function getKindList()
    {
        return ['help' => __('Kind help'), 'circle' => __('Kind circle'), 'dynamic' => __('Kind dynamic')];
    }


    public function getKindTextAttr($value, $data)
    {
        $value = $value ?: ($data['kind'] ?? '');
        $list = $this->getKindList();
        return $list[$value] ?? '';
    }




}
