<?php

namespace app\admin\controller;

use app\common\controller\Backend;

/**
 * VIP订单管理
 *
 * @icon fa fa-circle-o
 */
class VipOrder extends Backend
{

    /**
     * VipOrder模型对象
     * @var \app\admin\model\VipOrder
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\VipOrder;
        $this->view->assign("statusList", $this->model->getStatusList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams(null,true);

            $list = $this->model
                    ->with(['user', 'vip'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                // 用户信息
                if ($row->user) {
                    $row->getRelation('user')->visible(['id', 'nickname', 'avatar']);
                }

                // VIP套餐信息
                if ($row->vip) {
                    $row['vip_name_full'] = $row->vip->name;
                    $row['vip_day_full'] = $row->vip->day;
                    $row['vip_price_full'] = $row->vip->price;
                } else {
                    $row['vip_name_full'] = $row->vip_name ?: '';
                    $row['vip_day_full'] = $row->vip_day ?: '';
                    $row['vip_price_full'] = $row->vip_price ?: '';
                }

                // 格式化金额
                $row['vip_price'] = number_format($row->vip_price, 2);
                $row['amount'] = number_format($row->amount, 2);

                // 格式化时间
                $row['createtime_text'] = date('Y-m-d H:i:s', $row->createtime);
                $row['paytime_text'] = $row->paytime ? date('Y-m-d H:i:s', $row->paytime) : '';

                // 状态文本
                $row['status_text'] = $this->getStatusText($row->status);
                $row['paytype_text'] = $this->getPaytypeText($row->paytype);

                // 清理关联数据，避免输出过多数据
                unset($row->vip);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 获取状态文本
     */
    private function getStatusText($status)
    {
        $statusMap = [
            'pending' => '待支付',
            'paid' => '已支付'
        ];
        return $statusMap[$status] ?? '未知状态';
    }

    /**
     * 获取支付方式文本
     */
    private function getPaytypeText($paytype)
    {
        if (!$paytype) {
            return '';
        }
        $paytypeMap = [
            'wechat' => '微信支付',
            'alipay' => '支付宝'
        ];
        return $paytypeMap[$paytype] ?? $paytype;
    }

}
