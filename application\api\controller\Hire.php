<?php

namespace app\api\controller;

use app\common\controller\Api;
use think\Db;

/**
 * 企业认证接口
 */
class Hire extends Api
{
    protected $noNeedLogin = [];
    protected $noNeedRight = '*';

    /**
     * 提交企业认证
     *
     * @ApiMethod (POST)
     * @ApiParams (name="license_image", type="string", required=true, description="营业执照")
     * @ApiParams (name="name", type="string", required=true, description="公司名称")
     * @ApiParams (name="code", type="string", required=true, description="统一社会信用代码")
     * @ApiParams (name="front_image", type="string", required=true, description="身份证正面")
     * @ApiParams (name="reverse_image", type="string", required=true, description="身份证反面")
     * @ApiParams (name="legal_name", type="string", required=true, description="法人姓名")
     * @ApiParams (name="legal_card", type="string", required=true, description="法人身份证号")
     */
    public function apply()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $license_image = $this->request->post('license_image');
        $name = $this->request->post('name');
        $code = $this->request->post('code');
        $front_image = $this->request->post('front_image');
        $reverse_image = $this->request->post('reverse_image');
        $legal_name = $this->request->post('legal_name');
        $legal_card = $this->request->post('legal_card');

        // 验证必填参数
        if (!$license_image || !$name || !$code || !$front_image || !$reverse_image || !$legal_name || !$legal_card) {
            $this->error(__('Invalid parameters'));
        }

        // 检查是否已有认证记录
        $existingAuth = Db::name('hire_company')->where('user_id', $user->id)->find();
        if ($existingAuth) {
            // 如果已通过认证，不允许重新提交
            if ($existingAuth['status'] == 'pass') {
                $this->error('您已通过企业认证，无需重复提交');
            }
            // 如果是待审核状态，不允许重新提交
            if ($existingAuth['status'] == 'pending') {
                $this->error('您的认证申请正在审核中，请耐心等待');
            }
        }

        // 验证统一社会信用代码格式（18位）
        if (!preg_match('/^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/', $code)) {
            $this->error('统一社会信用代码格式不正确');
        }

        // 验证身份证号格式
        if (!preg_match('/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/', $legal_card)) {
            $this->error('法人身份证号格式不正确');
        }

        $data = [
            'user_id' => $user->id,
            'license_image' => $license_image,
            'name' => $name,
            'code' => $code,
            'front_image' => $front_image,
            'reverse_image' => $reverse_image,
            'legal_name' => $legal_name,
            'legal_card' => $legal_card,
            'status' => 'pending',
            'createtime' => time(),
            'refuse_remark' => null
        ];

        try {
            if ($existingAuth) {
                // 更新现有记录
                Db::name('hire_company')->where('id', $existingAuth['id'])->update($data);
                $id = $existingAuth['id'];
            } else {
                // 插入新记录
                $id = Db::name('hire_company')->insertGetId($data);
            }
        } catch (\Exception $e) {
            $this->error('认证申请提交失败，请重试');
        }

        $this->success('企业认证申请提交成功，请等待审核', ['id' => $id]);
    }

    /**
     * 获取认证状态
     *
     * @ApiMethod (GET)
     */
    public function getStatus()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $auth = Db::name('hire_company')->where('user_id', $user->id)->find();
        if (!$auth) {
            $this->success('获取成功', [
                'has_auth' => false,
                'status' => null,
                'status_text' => '未认证'
            ]);
        }

        $statusText = [
            'pending' => '审核中',
            'pass' => '认证通过',
            'refuse' => '认证被拒绝'
        ];

        $result = [
            'has_auth' => true,
            'id' => $auth['id'],
            'status' => $auth['status'],
            'status_text' => $statusText[$auth['status']] ?? '未知状态',
            'refuse_remark' => $auth['refuse_remark'] ?? '',
            'name' => $auth['name'],
            'code' => $auth['code'],
            'legal_name' => $auth['legal_name'],
            'createtime' => $auth['createtime']
        ];

        $this->success('获取成功', $result);
    }

    /**
     * 获取认证详情
     *
     * @ApiMethod (GET)
     */
    public function getDetail()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $auth = Db::name('hire_company')->where('user_id', $user->id)->find();
        if (!$auth) {
            $this->error('您还未提交企业认证申请');
        }

        $statusText = [
            'pending' => '审核中',
            'pass' => '认证通过',
            'refuse' => '认证被拒绝'
        ];

        $auth['status_text'] = $statusText[$auth['status']] ?? '未知状态';

        // 隐藏敏感字段
        unset($auth['user_id']);

        $this->success('获取成功', $auth);
    }

    /**
     * 编辑认证信息（仅限被拒绝状态）
     *
     * @ApiMethod (POST)
     * @ApiParams (name="license_image", type="string", required=true, description="营业执照")
     * @ApiParams (name="name", type="string", required=true, description="公司名称")
     * @ApiParams (name="code", type="string", required=true, description="统一社会信用代码")
     * @ApiParams (name="front_image", type="string", required=true, description="身份证正面")
     * @ApiParams (name="reverse_image", type="string", required=true, description="身份证反面")
     * @ApiParams (name="legal_name", type="string", required=true, description="法人姓名")
     * @ApiParams (name="legal_card", type="string", required=true, description="法人身份证号")
     */
    public function edit()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        // 检查认证记录
        $auth = Db::name('hire_company')->where('user_id', $user->id)->find();
        if (!$auth) {
            $this->error('您还未提交企业认证申请');
        }

        // 只有被拒绝的状态才能编辑
        if ($auth['status'] != 'refuse') {
            $this->error('只有认证被拒绝的情况下才能编辑');
        }

        $license_image = $this->request->post('license_image');
        $name = $this->request->post('name');
        $code = $this->request->post('code');
        $front_image = $this->request->post('front_image');
        $reverse_image = $this->request->post('reverse_image');
        $legal_name = $this->request->post('legal_name');
        $legal_card = $this->request->post('legal_card');

        // 验证必填参数
        if (!$license_image || !$name || !$code || !$front_image || !$reverse_image || !$legal_name || !$legal_card) {
            $this->error(__('Invalid parameters'));
        }

        // 验证统一社会信用代码格式（18位）
        if (!preg_match('/^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/', $code)) {
            $this->error('统一社会信用代码格式不正确');
        }

        // 验证身份证号格式
        if (!preg_match('/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/', $legal_card)) {
            $this->error('法人身份证号格式不正确');
        }

        $data = [
            'license_image' => $license_image,
            'name' => $name,
            'code' => $code,
            'front_image' => $front_image,
            'reverse_image' => $reverse_image,
            'legal_name' => $legal_name,
            'legal_card' => $legal_card,
            'status' => 'pending', // 重新提交审核
            'refuse_remark' => null // 清空拒绝原因
        ];

        try {
            Db::name('hire_company')->where('id', $auth['id'])->update($data);
        } catch (\Exception $e) {
            $this->error('修改失败，请重试');
        }

        $this->success('企业认证信息修改成功，请等待重新审核');
    }

    /**
     * 撤销认证申请（仅限待审核状态）
     *
     * @ApiMethod (POST)
     */
    public function cancel()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        // 检查认证记录
        $auth = Db::name('hire_company')->where('user_id', $user->id)->find();
        if (!$auth) {
            $this->error('您还未提交企业认证申请');
        }

        // 只有待审核状态才能撤销
        if ($auth['status'] != 'pending') {
            $this->error('只有审核中的申请才能撤销');
        }

        try {
            Db::name('hire_company')->where('id', $auth['id'])->delete();
        } catch (\Exception $e) {
            $this->error('撤销失败，请重试');
        }

        $this->success('认证申请已撤销');
    }

    /**
     * 发布招聘岗位
     *
     * @ApiMethod (POST)
     * @ApiParams (name="kind", type="string", required=true, description="类型:driver=司机,repair=修理工")
     * @ApiParams (name="post", type="string", required=true, description="岗位")
     * @ApiParams (name="username", type="string", required=true, description="联系人")
     * @ApiParams (name="mobile", type="string", required=true, description="联系电话")
     * @ApiParams (name="num", type="integer", required=true, description="人数需求")
     * @ApiParams (name="salary_range", type="string", required=true, description="薪资范围")
     * @ApiParams (name="address", type="string", required=true, description="工作地点")
     * @ApiParams (name="lng", type="string", required=false, description="经度")
     * @ApiParams (name="lat", type="string", required=false, description="纬度")
     * @ApiParams (name="province", type="string", required=false, description="省")
     * @ApiParams (name="city", type="string", required=false, description="市")
     * @ApiParams (name="district", type="string", required=false, description="区")
     * @ApiParams (name="content", type="string", required=false, description="职位详情")
     * @ApiParams (name="hire_car_id", type="integer", required=false, description="驾驶车型ID(司机类型必填)")
     * @ApiParams (name="hire_license_id", type="integer", required=false, description="驾照要求ID(司机和维修工类型必填)")
     * @ApiParams (name="hire_experience_id", type="string", required=false, description="从业经验ID(维修工类型必填)")
     * @ApiParams (name="hire_skills_ids", type="string", required=false, description="所需技能IDs,逗号分隔(维修工类型必填)")
     * @ApiParams (name="hire_post_ids", type="string", required=false, description="岗位福利IDs,逗号分隔")
     * @ApiParams (name="work_image", type="string", required=false, description="工作照片")
     * @ApiParams (name="car_image", type="string", required=false, description="车辆照片")
     */
    public function publishJob()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        // 获取参数
        $kind = $this->request->post('kind');
        $post = $this->request->post('post');
        $username = $this->request->post('username');
        $mobile = $this->request->post('mobile');
        $num = $this->request->post('num');
        $salary_range = $this->request->post('salary_range');
        $address = $this->request->post('address');
        $lng = $this->request->post('lng');
        $lat = $this->request->post('lat');
        $province = $this->request->post('province');
        $city = $this->request->post('city');
        $district = $this->request->post('district');
        $content = $this->request->post('content', '');
        $hire_car_id = $this->request->post('hire_car_id');
        $hire_license_id = $this->request->post('hire_license_id');
        $hire_experience_id = $this->request->post('hire_experience_id');
        $hire_skills_ids = $this->request->post('hire_skills_ids');
        $hire_post_ids = $this->request->post('hire_post_ids');
        $work_image = $this->request->post('work_image');
        $car_image = $this->request->post('car_image');

        // 验证必填参数
        if (!$kind || !$post || !$username || !$mobile || !$num || !$salary_range || !$address) {
            $this->error(__('Invalid parameters'));
        }

        // 验证类型
        if (!in_array($kind, ['driver', 'repair'])) {
            $this->error('类型只能是driver或repair');
        }

        // 验证手机号格式
        if (!preg_match('/^1[3-9]\d{9}$/', $mobile)) {
            $this->error('请输入正确的手机号');
        }

        // 验证人数
        if ($num <= 0) {
            $this->error('人数需求必须大于0');
        }

        // 司机类型的特殊验证
        if ($kind === 'driver') {
            if (!$hire_car_id || !$hire_license_id) {
                $this->error('司机类型需要填写驾驶车型和驾照要求');
            }
        }

        // 维修工类型的特殊验证
        if ($kind === 'repair') {
            if (!$hire_license_id || !$hire_experience_id || !$hire_skills_ids) {
                $this->error('维修工类型需要填写驾照要求、从业经验和所需技能');
            }
        }

        // 修理工类型的岗位验证
        if ($kind === 'repair') {
            if (!in_array($post, ['repair', 'tire'])) {
                $this->error('修理工岗位只能选择repair(机修工)或tire(补胎工)');
            }
        }

        $data = [
            'user_id' => $user['id'],
            'kind' => $kind,
            'post' => $post,
            'username' => $username,
            'mobile' => $mobile,
            'num' => intval($num),
            'salary_range' => $salary_range,
            'address' => $address,
            'lng' => $lng,
            'lat' => $lat,
            'province' => $province,
            'city' => $city,
            'district' => $district,
            'content' => $content,
            'hire_car_id' => $hire_car_id ? intval($hire_car_id) : null,
            'hire_license_id' => $hire_license_id ? intval($hire_license_id) : null,
            'hire_experience_id' => $hire_experience_id,
            'hire_skills_ids' => $hire_skills_ids,
            'hire_post_ids' => $hire_post_ids,
            'work_image' => $work_image,
            'car_image' => $car_image,
            'createtime' => time()
        ];

        try {
            $id = Db::name('hire')->insertGetId($data);
        } catch (\Exception $e) {
            $this->error('发布失败，请重试');
        }

        $this->success('招聘岗位发布成功', ['id' => $id]);
    }

    /**
     * 获取招聘岗位列表
     *
     * @ApiMethod (GET)
     * @ApiParams (name="kind", type="string", required=false, description="类型筛选:driver=司机,repair=修理工")
     * @ApiParams (name="page", type="integer", required=false, description="页码，默认1")
     * @ApiParams (name="limit", type="integer", required=false, description="每页数量，默认10")
     */
    public function getJobList()
    {
        $kind = $this->request->get('kind');
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);

        $where = [];
        if ($kind && in_array($kind, ['driver', 'repair'])) {
            $where['kind'] = $kind;
        }

        $list = Db::name('hire')
            ->where($where)
            ->order('createtime desc')
            ->page($page, $limit)
            ->select();

        $total = Db::name('hire')->where($where)->count();

        // 格式化数据
        foreach ($list as &$item) {
            $item['createtime_text'] = date('Y-m-d H:i:s', $item['createtime']);
            $item['kind_text'] = $item['kind'] === 'driver' ? '司机' : '修理工';

            // 解析关联数据
            if ($item['hire_car_id']) {
                $car = Db::name('hire_car')->where('id', $item['hire_car_id'])->find();
                $item['car_name'] = $car ? $car['name'] : '';
            }

            if ($item['hire_license_id']) {
                $license = Db::name('hire_license')->where('id', $item['hire_license_id'])->find();
                $item['license_name'] = $license ? $license['name'] : '';
            }

            if ($item['hire_experience_id']) {
                $experience = Db::name('hire_experience')->where('id', $item['hire_experience_id'])->find();
                $item['experience_name'] = $experience ? $experience['name'] : '';
            }

            // 解析技能
            if ($item['hire_skills_ids']) {
                $skillIds = explode(',', $item['hire_skills_ids']);
                $skills = Db::name('hire_skills')->where('id', 'in', $skillIds)->column('name');
                $item['skills_names'] = $skills;
            }

            // 解析福利
            if ($item['hire_post_ids']) {
                $postIds = explode(',', $item['hire_post_ids']);
                $posts = Db::name('hire_post')->where('id', 'in', $postIds)->column('name');
                $item['post_names'] = $posts;
            }
        }

        $this->success('获取成功', [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit
        ]);
    }

    /**
     * 获取招聘岗位详情
     *
     * @ApiMethod (GET)
     * @ApiParams (name="id", type="integer", required=true, description="招聘ID")
     */
    public function getJobDetail()
    {
        $id = $this->request->get('id');
        if (!$id) {
            $this->error(__('Invalid parameters'));
        }

        $job = Db::name('hire')->where('id', $id)->find();
        if (!$job) {
            $this->error('招聘信息不存在');
        }

        // 格式化数据
        $job['createtime_text'] = date('Y-m-d H:i:s', $job['createtime']);
        $job['kind_text'] = $job['kind'] === 'driver' ? '司机' : '修理工';

        // 解析关联数据
        if ($job['hire_car_id']) {
            $car = Db::name('hire_car')->where('id', $job['hire_car_id'])->find();
            $job['car_name'] = $car ? $car['name'] : '';
        }

        if ($job['hire_license_id']) {
            $license = Db::name('hire_license')->where('id', $job['hire_license_id'])->find();
            $job['license_name'] = $license ? $license['name'] : '';
        }

        if ($job['hire_experience_id']) {
            $experience = Db::name('hire_experience')->where('id', $job['hire_experience_id'])->find();
            $job['experience_name'] = $experience ? $experience['name'] : '';
        }

        // 解析技能
        if ($job['hire_skills_ids']) {
            $skillIds = explode(',', $job['hire_skills_ids']);
            $skills = Db::name('hire_skills')->where('id', 'in', $skillIds)->select();
            $job['skills'] = $skills;
        }

        // 解析福利
        if ($job['hire_post_ids']) {
            $postIds = explode(',', $job['hire_post_ids']);
            $posts = Db::name('hire_post')->where('id', 'in', $postIds)->select();
            $job['posts'] = $posts;
        }

        $this->success('获取成功', $job);
    }

    /**
     * 获取我发布的招聘列表
     *
     * @ApiMethod (GET)
     * @ApiParams (name="kind", type="string", required=false, description="类型筛选:driver=司机,repair=修理工")
     * @ApiParams (name="page", type="integer", required=false, description="页码，默认1")
     * @ApiParams (name="limit", type="integer", required=false, description="每页数量，默认10")
     */
    public function getMyJobList()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $kind = $this->request->get('kind');
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);

        $where = ['user_id' => $user['id']];
        if ($kind && in_array($kind, ['driver', 'repair'])) {
            $where['kind'] = $kind;
        }

        $list = Db::name('hire')
            ->where($where)
            ->order('createtime desc')
            ->page($page, $limit)
            ->select();

        $total = Db::name('hire')->where($where)->count();

        // 格式化数据
        foreach ($list as &$item) {
            $item['createtime_text'] = date('Y-m-d H:i:s', $item['createtime']);
            $item['kind_text'] = $item['kind'] === 'driver' ? '司机' : '修理工';

            // 岗位名称转换
            if ($item['kind'] === 'repair') {
                $item['post_text'] = $item['post'] === 'repair' ? '机修工' : '补胎工';
            } else {
                $item['post_text'] = $item['post'];
            }

            // 解析关联数据
            if ($item['hire_car_id']) {
                $car = Db::name('hire_car')->where('id', $item['hire_car_id'])->find();
                $item['car_name'] = $car ? $car['name'] : '';
            }

            if ($item['hire_license_id']) {
                $license = Db::name('hire_license')->where('id', $item['hire_license_id'])->find();
                $item['license_name'] = $license ? $license['name'] : '';
            }

            if ($item['hire_experience_id']) {
                $experience = Db::name('hire_experience')->where('id', $item['hire_experience_id'])->find();
                $item['experience_name'] = $experience ? $experience['name'] : '';
            }

            // 解析技能
            if ($item['hire_skills_ids']) {
                $skillIds = explode(',', $item['hire_skills_ids']);
                $skills = Db::name('hire_skills')->where('id', 'in', $skillIds)->column('name');
                $item['skills_names'] = $skills;
            }

            // 解析福利
            if ($item['hire_post_ids']) {
                $postIds = explode(',', $item['hire_post_ids']);
                $posts = Db::name('hire_post')->where('id', 'in', $postIds)->column('name');
                $item['post_names'] = $posts;
            }
        }

        $this->success('获取成功', [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit
        ]);
    }

    /**
     * 编辑招聘岗位
     *
     * @ApiMethod (POST)
     * @ApiParams (name="id", type="integer", required=true, description="招聘ID")
     * @ApiParams (name="kind", type="string", required=true, description="类型:driver=司机,repair=修理工")
     * @ApiParams (name="post", type="string", required=true, description="岗位:repair=机修工,tire=补胎工")
     * @ApiParams (name="username", type="string", required=true, description="联系人")
     * @ApiParams (name="mobile", type="string", required=true, description="联系电话")
     * @ApiParams (name="num", type="integer", required=true, description="人数需求")
     * @ApiParams (name="salary_range", type="string", required=true, description="薪资范围")
     * @ApiParams (name="address", type="string", required=true, description="工作地点")
     * @ApiParams (name="lng", type="string", required=false, description="经度")
     * @ApiParams (name="lat", type="string", required=false, description="纬度")
     * @ApiParams (name="province", type="string", required=false, description="省")
     * @ApiParams (name="city", type="string", required=false, description="市")
     * @ApiParams (name="district", type="string", required=false, description="区")
     * @ApiParams (name="content", type="string", required=false, description="职位详情")
     * @ApiParams (name="hire_car_id", type="integer", required=false, description="驾驶车型ID(司机类型必填)")
     * @ApiParams (name="hire_license_id", type="integer", required=false, description="驾照要求ID(司机和维修工类型必填)")
     * @ApiParams (name="hire_experience_id", type="string", required=false, description="从业经验ID(维修工类型必填)")
     * @ApiParams (name="hire_skills_ids", type="string", required=false, description="所需技能IDs,逗号分隔(维修工类型必填)")
     * @ApiParams (name="hire_post_ids", type="string", required=false, description="岗位福利IDs,逗号分隔")
     * @ApiParams (name="work_image", type="string", required=false, description="工作照片")
     * @ApiParams (name="car_image", type="string", required=false, description="车辆照片")
     */
    public function editJob()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        // 获取参数
        $id = $this->request->post('id');
        $kind = $this->request->post('kind');
        $post = $this->request->post('post');
        $username = $this->request->post('username');
        $mobile = $this->request->post('mobile');
        $num = $this->request->post('num');
        $salary_range = $this->request->post('salary_range');
        $address = $this->request->post('address');
        $lng = $this->request->post('lng');
        $lat = $this->request->post('lat');
        $province = $this->request->post('province');
        $city = $this->request->post('city');
        $district = $this->request->post('district');
        $content = $this->request->post('content', '');
        $hire_car_id = $this->request->post('hire_car_id');
        $hire_license_id = $this->request->post('hire_license_id');
        $hire_experience_id = $this->request->post('hire_experience_id');
        $hire_skills_ids = $this->request->post('hire_skills_ids');
        $hire_post_ids = $this->request->post('hire_post_ids');
        $work_image = $this->request->post('work_image');
        $car_image = $this->request->post('car_image');

        // 验证必填参数
        if (!$id || !$kind || !$post || !$username || !$mobile || !$num || !$salary_range || !$address) {
            $this->error(__('Invalid parameters'));
        }

        // 检查招聘是否存在且属于当前用户
        $job = Db::name('hire')->where(['id' => $id, 'user_id' => $user['id']])->find();
        if (!$job) {
            $this->error('招聘信息不存在或无权限编辑');
        }

        // 验证类型
        if (!in_array($kind, ['driver', 'repair'])) {
            $this->error('类型只能是driver或repair');
        }

        // 验证手机号格式
        if (!preg_match('/^1[3-9]\d{9}$/', $mobile)) {
            $this->error('请输入正确的手机号');
        }

        // 验证人数
        if ($num <= 0) {
            $this->error('人数需求必须大于0');
        }

        // 司机类型的特殊验证
        if ($kind === 'driver') {
            if (!$hire_car_id || !$hire_license_id) {
                $this->error('司机类型需要填写驾驶车型和驾照要求');
            }
        }

        // 维修工类型的特殊验证
        if ($kind === 'repair') {
            if (!$hire_license_id || !$hire_experience_id || !$hire_skills_ids) {
                $this->error('维修工类型需要填写驾照要求、从业经验和所需技能');
            }
        }

        // 修理工类型的岗位验证
        if ($kind === 'repair') {
            if (!in_array($post, ['repair', 'tire'])) {
                $this->error('修理工岗位只能选择repair(机修工)或tire(补胎工)');
            }
        }

        $data = [
            'kind' => $kind,
            'post' => $post,
            'username' => $username,
            'mobile' => $mobile,
            'num' => intval($num),
            'salary_range' => $salary_range,
            'address' => $address,
            'lng' => $lng,
            'lat' => $lat,
            'province' => $province,
            'city' => $city,
            'district' => $district,
            'content' => $content,
            'hire_car_id' => $hire_car_id ? intval($hire_car_id) : null,
            'hire_license_id' => $hire_license_id ? intval($hire_license_id) : null,
            'hire_experience_id' => $hire_experience_id,
            'hire_skills_ids' => $hire_skills_ids,
            'hire_post_ids' => $hire_post_ids,
            'work_image' => $work_image,
            'car_image' => $car_image
        ];

        try {
            Db::name('hire')->where('id', $id)->update($data);
        } catch (\Exception $e) {
            $this->error('编辑失败，请重试');
        }

        $this->success('招聘岗位编辑成功');
    }

    /**
     * 删除招聘岗位
     *
     * @ApiMethod (POST)
     * @ApiParams (name="id", type="integer", required=true, description="招聘ID")
     */
    public function deleteJob()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $id = $this->request->post('id');
        if (!$id) {
            $this->error(__('Invalid parameters'));
        }

        // 检查招聘是否存在且属于当前用户
        $job = Db::name('hire')->where(['id' => $id, 'user_id' => $user['id']])->find();
        if (!$job) {
            $this->error('招聘信息不存在或无权限删除');
        }

        try {
            Db::name('hire')->where('id', $id)->delete();
        } catch (\Exception $e) {
            $this->error('删除失败，请重试');
        }

        $this->success('招聘岗位删除成功');
    }
}
