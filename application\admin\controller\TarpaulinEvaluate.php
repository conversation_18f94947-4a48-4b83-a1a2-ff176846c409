<?php

namespace app\admin\controller;

use app\common\controller\Backend;

/**
 * 篷布定制标题
 *
 * @icon fa fa-circle-o
 */
class TarpaulinEvaluate extends Backend
{

    /**
     * TarpaulinEvaluate模型对象
     * @var \app\admin\model\TarpaulinEvaluate
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\TarpaulinEvaluate;

    }



    /**
     * 查看
     *
     * @return string|Json
     * @throws \think\Exception
     * @throws DbException
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);

        // 获取tarpaulin_id参数
        $tarpaulin_id = $this->request->get('tarpaulin_id', '');

        if (false === $this->request->isAjax()) {
            // 在非Ajax请求时（页面首次加载）设置配置，确保JavaScript能获取到
            $this->assignconfig('tarpaulin_id', $tarpaulin_id);
            return $this->view->fetch();
        }

        //如果发送的来源是 Selectpage，则转发到 Selectpage
        if ($this->request->request('keyField')) {
            return $this->selectpage();
        }

        [$where, $sort, $order, $offset, $limit] = $this->buildparams();



        $list = $this->model
            ->where($where)
            ->order($sort, $order)
            ->paginate($limit);
        $result = ['total' => $list->total(), 'rows' => $list->items()];
        return json($result);
    }

    /**
     * 添加
     *
     * @return string
     * @throws \think\Exception
     */
    public function add()
    {
        if (false === $this->request->isPost()) {
            // 获取tarpaulin_id参数
            $tarpaulin_id = $this->request->get('tarpaulin_id', '');

            // 将tarpaulin_id传递给视图
            $this->view->assign('tarpaulin_id', $tarpaulin_id);
            return $this->view->fetch();
        }

        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }

        $params = $this->preExcludeFields($params);

        $result = false;
        \think\Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                $this->model->validateFailException()->validate($validate);
            }
            $result = $this->model->allowField(true)->save($params);

            // 添加评价后，更新tarpaulin表的评分统计
            if ($result && !empty($params['tarpaulin_id'])) {
                $this->updateTarpaulinRating($params['tarpaulin_id']);
            }

            \think\Db::commit();
        } catch (\think\Exception $e) {
            \think\Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were inserted'));
        }
        $this->success();
    }

    /**
     * 编辑
     *
     * @param $ids
     * @return string
     * @throws \think\db\exception\DbException
     * @throws \think\Exception
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }
        if (false === $this->request->isPost()) {
            $this->view->assign('row', $row);
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        // 保存原来的tarpaulin_id，用于更新评分
        $oldTarpaulinId = $row->tarpaulin_id;

        $result = false;
        \think\Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                $row->validateFailException()->validate($validate);
            }
            $result = $row->allowField(true)->save($params);

            // 编辑评价后，更新相关tarpaulin表的评分统计
            if ($result) {
                // 如果tarpaulin_id发生了变化，需要更新两个tarpaulin的评分
                if (!empty($params['tarpaulin_id']) && $params['tarpaulin_id'] != $oldTarpaulinId) {
                    $this->updateTarpaulinRating($oldTarpaulinId); // 更新原来的
                    $this->updateTarpaulinRating($params['tarpaulin_id']); // 更新新的
                } else {
                    // 如果tarpaulin_id没变，只更新当前的
                    $this->updateTarpaulinRating($oldTarpaulinId);
                }
            }

            \think\Db::commit();
        } catch (\think\Exception $e) {
            \think\Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success();
    }

    /**
     * 删除
     *
     * @param $ids
     * @return void
     * @throws \think\db\exception\DbException
     * @throws \think\Exception
     */
    public function del($ids = null)
    {
        if (false === $this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ?: $this->request->post("ids");
        if (empty($ids)) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }
        $pk = $this->model->getPk();
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            $this->model->where($this->dataLimitField, 'in', $adminIds);
        }
        $list = $this->model->where($pk, 'in', $ids)->select();

        $count = 0;
        \think\Db::startTrans();
        try {
            // 收集需要更新评分的tarpaulin_id
            $tarpaulinIds = [];
            foreach ($list as $item) {
                if (!empty($item->tarpaulin_id)) {
                    $tarpaulinIds[] = $item->tarpaulin_id;
                }
                $count += $item->delete();
            }

            // 删除评价后，更新相关tarpaulin表的评分统计
            foreach (array_unique($tarpaulinIds) as $tarpaulinId) {
                $this->updateTarpaulinRating($tarpaulinId);
            }

            \think\Db::commit();
        } catch (\think\Exception $e) {
            \think\Db::rollback();
            $this->error($e->getMessage());
        }
        if ($count) {
            $this->success();
        } else {
            $this->error(__('No rows were deleted'));
        }
    }

    /**
     * 更新tarpaulin表的评分统计
     *
     * @param int $tarpaulinId
     * @return void
     */
    private function updateTarpaulinRating($tarpaulinId)
    {
        if (empty($tarpaulinId)) {
            return;
        }

        // 计算该tarpaulin的评分统计
        $stats = \think\Db::name('tarpaulin_evaluate')
            ->where('tarpaulin_id', $tarpaulinId)
            ->field('COUNT(*) as rate_num, AVG(star) as avg_rate')
            ->find();

        $rateNum = intval($stats['rate_num']);
        $avgRate = $rateNum > 0 ? round($stats['avg_rate'], 1) : 0;

        // 更新tarpaulin表
        \think\Db::name('tarpaulin')
            ->where('id', $tarpaulinId)
            ->update([
                'rate' => $avgRate,
                'rate_num' => $rateNum
            ]);
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */
}
