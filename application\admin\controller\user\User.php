<?php

namespace app\admin\controller\user;

use app\common\controller\Backend;
use app\common\library\Auth;

/**
 * 会员管理
 *
 * @icon fa fa-user
 */
class User extends Backend
{

    protected $relationSearch = true;
    protected $searchFields = 'id,username,nickname';

    /**
     * @var \app\admin\model\User
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\User;
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $list = $this->model
                ->with('group')
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);
            foreach ($list as $k => $v) {
                $v->avatar = $v->avatar ? cdnurl($v->avatar, true) : letter_avatar($v->nickname);
                $v->hidden(['password', 'salt']);
            }
            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $this->token();
        }
        return parent::add();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        if ($this->request->isPost()) {
            $this->token();
        }
        $row = $this->model->get($ids);
        $this->modelValidate = false;
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $this->view->assign('groupList', build_select('row[group_id]', \app\admin\model\UserGroup::column('id,name'), $row['group_id'], ['class' => 'form-control selectpicker']));
        return parent::edit($ids);
    }

    /**
     * VIP管理页面
     */
    public function vip()
    {
        $user_id = $this->request->get('user_id');
        if (!$user_id) {
            $this->error('用户ID不能为空');
        }
        $user = $this->model->get($user_id);
        if (!$user) {
            $this->error('用户不存在');
        }
        if ($this->request->isPost()) {
            list($action, $days, $endtime) = array_values($this->request->post('row/a'));
            try {
                $currentTime = time();
                switch ($action) {
                    case 'add':
                        // 按天数计算
                        if ($user->is_vip == 1 && $user->endtime > $currentTime) {
                            // 如果当前是VIP且未过期，在原有基础上增加天数
                            $newEndtime = $user->endtime + ($days * 86400);
                        } else {
                            // 如果不是VIP或已过期，从当前时间开始计算
                            $newEndtime = $currentTime + ($days * 86400);
                        }
                        $user->is_vip = 1;
                        $user->endtime = $newEndtime;
                        break;
                    case 'set':
                        // 设置VIP时长（从当前时间开始计算）
                        if (!empty($endtime)) {
                            // 如果指定了具体结束时间
                            $newEndtime = strtotime($endtime);
                        } else {
                            // 按天数计算
                            $newEndtime = $currentTime + ($days * 86400);
                        }
                        $user->is_vip = 1;
                        $user->endtime = $newEndtime;
                        break;
                    case 'remove':
                        // 取消VIP
                        $user->is_vip = 0;
                        $user->endtime = 0;
                        break;
                    default:
                        $this->error('无效的操作类型');
                }

                $user->save();
                $actionText = [
                    'add' => '增加VIP时长',
                    'set' => '设置VIP时长',
                    'remove' => '取消VIP'
                ];
            } catch (\Exception $e) {
                $this->error('操作失败：' . $e->getMessage());
            }
            $this->success($actionText[$action] . '成功');
        }

        $this->view->assign('user_id', $user_id);
        $this->view->assign('user', $user);
        return $this->view->fetch();
    }


    /**
     * 删除
     */
    public function del($ids = "")
    {
        if (!$this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ? $ids : $this->request->post("ids");
        $row = $this->model->get($ids);
        $this->modelValidate = true;
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        Auth::instance()->delete($row['id']);
        $this->success();
    }
}
