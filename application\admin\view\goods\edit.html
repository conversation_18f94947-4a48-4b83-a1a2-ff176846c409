<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Car_type')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-car_type" data-rule="required" class="form-control selectpicker" name="row[car_type]">
                {foreach name="carTypeList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.car_type"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Time_type')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-time_type" data-rule="required" class="form-control selectpicker" name="row[time_type]">
                {foreach name="timeTypeList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.time_type"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Get_address_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-get_address_id" data-rule="required" data-source="get/address/index" class="form-control selectpage" name="row[get_address_id]" type="text" value="{$row.get_address_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Send_address_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-send_address_id" data-rule="required" data-source="send/address/index" class="form-control selectpage" name="row[send_address_id]" type="text" value="{$row.send_address_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Get_date')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-get_date" class="form-control" name="row[get_date]" type="text" value="{$row.get_date|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Get_datetime')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-get_datetime" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[get_datetime]" type="text" value="{:$row.get_datetime?datetime($row.get_datetime):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Send_date')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-send_date" class="form-control" name="row[send_date]" type="text" value="{$row.send_date|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Send_datetime')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-send_datetime" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[send_datetime]" type="text" value="{:$row.send_datetime?datetime($row.send_datetime):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Goods_type')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-goods_type" class="form-control" name="row[goods_type]" type="text" value="{$row.goods_type|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Weight')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-weight" class="form-control" name="row[weight]" type="text" value="{$row.weight|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Volume')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-volume" class="form-control" name="row[volume]" type="text" value="{$row.volume|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Long')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-long" class="form-control" name="row[long]" type="text" value="{$row.long|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Remark')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-remark" class="form-control" name="row[remark]" type="text" value="{$row.remark|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Mobile')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-mobile" class="form-control" name="row[mobile]" type="text" value="{$row.mobile|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-rule="required" data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[user_id]" type="text" value="{$row.user_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Orderid')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-orderid" class="form-control" name="row[orderid]" type="text" value="{$row.orderid|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Paytype')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-paytype" class="form-control" name="row[paytype]" type="text" value="{$row.paytype|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Amount')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-amount" data-rule="required" class="form-control" step="0.01" name="row[amount]" type="number" value="{$row.amount|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Paytime')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-paytime" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[paytime]" type="text" value="{:$row.paytime?datetime($row.paytime):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Transid')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-transid" class="form-control" name="row[transid]" type="text" value="{$row.transid|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Refuse_remark')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-refuse_remark" class="form-control" name="row[refuse_remark]" type="text" value="{$row.refuse_remark|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is_show')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-is_show" data-rule="required" class="form-control" name="row[is_show]" type="text" value="{$row.is_show|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Distance')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-distance" class="form-control" step="0.01" name="row[distance]" type="number" value="{$row.distance|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Goods_car_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-goods_car_id" data-rule="required" data-source="goods/car/index" class="form-control selectpage" name="row[goods_car_id]" type="text" value="{$row.goods_car_id|htmlentities}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
