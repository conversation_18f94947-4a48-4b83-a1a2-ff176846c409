<?php

namespace app\admin\model;

use think\Model;


class TarpaulinEvaluate extends Model
{

    

    

    // 表名
    protected $name = 'tarpaulin_evaluate';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'star_text'
    ];
    

    protected static function init()
    {
        self::beforeWrite(function ($row) {
            if ($row['star'] < 1 || $row['star'] > 5) {
                throw new \think\Exception('评分只能输入1-5之间的整数');
            }
        });

        self::afterInsert(function ($row) {
            if (!$row['weigh']) {
                $pk = $row->getPk();
                $row->getQuery()->where($pk, $row[$pk])->update(['weigh' => $row[$pk]]);
            }
        });
    }

    







    public function getStarTextAttr($value, $data)
    {
        $star = $data['star'] ?? 0;
        if ($star >= 4) {
            return '好评';
        } elseif ($star <= 3) {
            return '差评';
        }
        return '中评';
    }

    public function user()
    {
        return $this->belongsTo('User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    public function tarpaulin()
    {
        return $this->belongsTo('Tarpaulin', 'tarpaulin_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
