<?php

namespace app\admin\controller;

use app\common\controller\Backend;

/**
 * 流动补胎评价
 *
 * @icon fa fa-circle-o
 */
class TireEvaluate extends Backend
{

    /**
     * TireEvaluate模型对象
     * @var \app\admin\model\TireEvaluate
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\TireEvaluate;

    }



    /**
     * 查看
     *
     * @return string|Json
     * @throws \think\Exception
     * @throws DbException
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);

        // 获取tire_id参数
        $tire_id = $this->request->get('tire_id', '');

        if (false === $this->request->isAjax()) {
            // 在非Ajax请求时（页面首次加载）设置配置，确保JavaScript能获取到
            $this->assignconfig('tire_id', $tire_id);
            return $this->view->fetch();
        }

        //如果发送的来源是 Selectpage，则转发到 Selectpage
        if ($this->request->request('keyField')) {
            return $this->selectpage();
        }

        [$where, $sort, $order, $offset, $limit] = $this->buildparams();


        $list = $this->model
            ->where($where)
            ->order($sort, $order)
            ->paginate($limit);
        $result = ['total' => $list->total(), 'rows' => $list->items()];
        return json($result);
    }

    /**
     * 添加
     *
     * @return string
     * @throws \think\Exception
     */
    public function add()
    {
        if (false === $this->request->isPost()) {
            // 获取tire_id参数
            $tire_id = $this->request->get('tire_id', '');

            // 将tire_id传递给视图
            $this->view->assign('tire_id', $tire_id);
            return $this->view->fetch();
        }

        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }

        $params = $this->preExcludeFields($params);

        $result = false;
        \think\Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                $this->model->validateFailException()->validate($validate);
            }
            $result = $this->model->allowField(true)->save($params);

            // 添加评价后，更新tire表的评分统计
            if ($result && !empty($params['tire_id'])) {
                $this->updateTireRating($params['tire_id']);
            }

            \think\Db::commit();
        } catch (\think\Exception $e) {
            \think\Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were inserted'));
        }
        $this->success();
    }

    /**
     * 编辑
     *
     * @param $ids
     * @return string
     * @throws \think\db\exception\DbException
     * @throws \think\Exception
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }
        if (false === $this->request->isPost()) {
            $this->view->assign('row', $row);
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        // 保存原来的tire_id，用于更新评分
        $oldTireId = $row->tire_id;

        $result = false;
        \think\Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                $row->validateFailException()->validate($validate);
            }
            $result = $row->allowField(true)->save($params);

            // 编辑评价后，更新相关tire表的评分统计
            if ($result) {
                // 如果tire_id发生了变化，需要更新两个tire的评分
                if (!empty($params['tire_id']) && $params['tire_id'] != $oldTireId) {
                    $this->updateTireRating($oldTireId); // 更新原来的
                    $this->updateTireRating($params['tire_id']); // 更新新的
                } else {
                    // 如果tire_id没变，只更新当前的
                    $this->updateTireRating($oldTireId);
                }
            }

            \think\Db::commit();
        } catch (\think\Exception $e) {
            \think\Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success();
    }

    /**
     * 删除
     *
     * @param $ids
     * @return void
     * @throws \think\db\exception\DbException
     * @throws \think\Exception
     */
    public function del($ids = null)
    {
        if (false === $this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ?: $this->request->post("ids");
        if (empty($ids)) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }
        $pk = $this->model->getPk();
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            $this->model->where($this->dataLimitField, 'in', $adminIds);
        }
        $list = $this->model->where($pk, 'in', $ids)->select();

        $count = 0;
        \think\Db::startTrans();
        try {
            // 收集需要更新评分的tire_id
            $tireIds = [];
            foreach ($list as $item) {
                if (!empty($item->tire_id)) {
                    $tireIds[] = $item->tire_id;
                }
                $count += $item->delete();
            }

            // 删除评价后，更新相关tire表的评分统计
            foreach (array_unique($tireIds) as $tireId) {
                $this->updateTireRating($tireId);
            }

            \think\Db::commit();
        } catch (\think\Exception $e) {
            \think\Db::rollback();
            $this->error($e->getMessage());
        }
        if ($count) {
            $this->success();
        } else {
            $this->error(__('No rows were deleted'));
        }
    }

    /**
     * 更新tire表的评分统计
     *
     * @param int $tireId
     * @return void
     */
    private function updateTireRating($tireId)
    {
        if (empty($tireId)) {
            return;
        }

        // 计算该tire的评分统计
        $stats = \think\Db::name('tire_evaluate')
            ->where('tire_id', $tireId)
            ->field('COUNT(*) as rate_num, AVG(star) as avg_rate')
            ->find();

        $rateNum = intval($stats['rate_num']);
        $avgRate = $rateNum > 0 ? round($stats['avg_rate'], 1) : 0;

        // 更新tire表
        \think\Db::name('tire')
            ->where('id', $tireId)
            ->update([
                'rate' => $avgRate,
                'rate_num' => $rateNum
            ]);
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */
}
