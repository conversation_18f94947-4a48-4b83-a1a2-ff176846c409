<?php

namespace app\api\controller;

use app\common\controller\Api;
use addons\epay\library\Service;
use think\Db;

/**
 * 订单接口
 */
class Order extends Api
{
    protected $noNeedLogin = ['notifyx'];
    protected $noNeedRight = '*';

    /**
     * 创建订单
     *
     * @ApiMethod (POST)
     * @ApiParams (name="company_category_id", type="int", required=true, description="企业分类ID")
     * @ApiParams (name="link_id", type="int", required=true, description="关联ID")
     * @ApiParams (name="amount", type="decimal", required=true, description="订单金额")
     */
    public function createOrder()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $company_category_id = $this->request->post('company_category_id');
        $link_id = $this->request->post('link_id');

        $amount = config('site.once_amount');

        // 验证必填参数
        if (!$company_category_id || !$link_id || !$amount) {
            $this->error(__('Invalid parameters'));
        }

        // 确保金额不为0
        if ($amount <= 0) {
            $amount = 0.01;
        }

        // 检查是否已存在未支付的订单
        $existingOrder = Db::name('order')
            ->where('user_id', $user->id)
            ->where('company_category_id', $company_category_id)
            ->where('link_id', $link_id)
            ->where('status', 'pending')
            ->find();

        if ($existingOrder) {
            $this->success('订单已存在', [
                'order_id' => $existingOrder['id'],
                'orderid' => $existingOrder['orderid'],
                'amount' => $existingOrder['amount'],
                'status' => $existingOrder['status']
            ]);
        }

        // 生成订单号
        $orderid = 'ORDER' . date('YmdHis') . rand(1000, 9999);



        // 创建订单数据
        $orderData = [
            'user_id' => $user->id,
            'company_category_id' => $company_category_id,
            'link_id' => $link_id,
            'orderid' => $orderid,
            'amount' => $amount,
            'status' => 'pending',
            'createtime' => time()
        ];

        try {
            Db::startTrans();
            // 插入订单
            $order_id = Db::name('order')->insertGetId($orderData);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->error('订单创建失败，请重试');
        }

        $this->success('订单创建成功', [
            'order_id' => $order_id,
            'orderid' => $orderid,
            'amount' => $amount,
            'status' => 'pending'
        ]);
    }

    /**
     * 支付订单
     *
     * @ApiMethod (POST)
     * @ApiParams (name="order_id", type="int", required=true, description="订单ID")
     * @ApiParams (name="pay_type", type="string", required=true, description="支付方式:wechat=微信,alipay=支付宝")
     * @ApiParams (name="test", type="int", required=false, description="测试模式:1=测试,0=正式")
     */
    public function pay()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $orderid = $this->request->post('orderid');
        $pay_type = $this->request->post('pay_type');
        $test = $this->request->post('test', 0);

        // 验证必填参数
        if (!$orderid || !$pay_type) {
            $this->error(__('Invalid parameters'));
        }

        // 验证支付方式
        if (!in_array($pay_type, ['wechat', 'alipay'])) {
            $this->error('支付方式参数错误');
        }

        // 查询订单信息
        $order = Db::name('order')
            ->where('orderid', $orderid)
            ->where('user_id', $user->id)
            ->find();

        if (!$order) {
            $this->error('订单不存在');
        }

        // 检查订单状态
        if ($order['status'] == 'paid') {
            $this->error('订单已支付');
        }

        // 重新生成订单号（防止第三方支付重复提交）
        $newOrderid = 'ORDER' . date('YmdHis') . rand(1000, 9999);

        try {
            // 更新订单号
            Db::name('order')->where('id', $order['id'])->update([
                'orderid' => $newOrderid
            ]);
            $order['orderid'] = $newOrderid; // 更新本地变量
        } catch (\Exception $e) {
            $this->error('订单更新失败，请重试');
        }

        // 测试模式直接回调
        if ($test) {
            $this->payCallback($order['orderid'], '88888888', $pay_type);
            $this->success('支付成功');
        }

        // 微信支付需要openid
        if ($pay_type == 'wechat' && !$openid = $this->auth->openid) {
            $this->error('请先绑定微信');
        }

        $notifyurl = $this->request->domain() . '/api/order/notifyx/paytype/' . $pay_type;

        // 调用支付接口
        try {
            $payResult = Service::submitOrder($order['amount'], $order['orderid'], $pay_type, '订单支付', $notifyurl, null, 'app', $openid ?? '');
        } catch (\Exception $e) {
            $this->error('支付失败，请重试');
        }

        $this->success('支付订单创建成功', $payResult);
    }

    /**
     * 支付回调通知
     */
    public function notifyx()
    {
        $paytype = $this->request->param('paytype');
        $pay = Service::checkNotify($paytype);
        if (!$pay) {
            return json(['code' => 'FAIL', 'message' => '失败'], 500, ['Content-Type' => 'application/json']);
        }

        // 获取回调数据，V3和V2的回调接收不同
        $data = Service::isVersionV3() ? $pay->callback() : $pay->verify();

        try {
            //微信支付V3返回和V2不同
            if (Service::isVersionV3() && $paytype === 'wechat') {
                $data = $data['resource']['ciphertext'];
                $data['total_fee'] = $data['amount']['total'];
            }
            \think\Log::record($data);

            //获取支付金额、订单号
            $payamount = $paytype == 'alipay' ? $data['total_amount'] : $data['total_fee'] / 100;
            $out_trade_no = $data['out_trade_no'];

            $this->payCallback($out_trade_no, $data['transaction_id'], $paytype);
            \think\Log::record("回调成功，订单号：{$out_trade_no}，金额：{$payamount}");
        } catch (\Exception $e) {
            \think\Log::record("回调逻辑处理错误:" . $e->getMessage(), "error");
        }

        //下面这句必须要执行,且在此之前不能有任何输出
        if (Service::isVersionV3()) {
            return $pay->success()->getBody()->getContents();
        } else {
            return $pay->success()->send();
        }
    }

    /**
     * 支付回调处理
     *
     * @param string $orderid 订单号
     * @param string $trade_no 第三方交易号
     * @param string $pay_type 支付方式
     */
    public function payCallback($orderid, $trade_no, $pay_type)
    {
        // 验证必填参数
        if (!$orderid || !$trade_no || !$pay_type) {
            $this->error(__('Invalid parameters'));
        }

        // 查询订单
        $order = Db::name('order')->where('orderid', $orderid)->find();
        if (!$order) {
            $this->error('订单不存在');
        }

        // 检查订单状态
        if ($order['status'] == 'paid') {
            $this->success('订单已支付');
        }

        try {
            Db::startTrans();
            // 更新订单状态
            Db::name('order')->where('id', $order['id'])->update([
                'status' => 'paid',
                'paytype' => $pay_type,
                'paytime' => time(),
                'transid' => $trade_no
            ]);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->error('支付处理失败');
        }

        $this->success('支付成功');
    }

    /**
     * 获取订单列表
     *
     * @ApiMethod (GET)
     * @ApiParams (name="page", type="integer", required=false, description="页码")
     * @ApiParams (name="limit", type="integer", required=false, description="每页数量")
     * @ApiParams (name="status", type="string", required=false, description="订单状态:pending=待支付,paid=已支付")
     */
    public function getOrderList()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);
        $status = $this->request->get('status', '');

        $query = Db::name('order')->where('user_id', $user->id);

        // 状态筛选
        if (!empty($status)) {
            $query->where('status', $status);
        }

        $list = $query->order('createtime', 'desc')
            ->paginate($limit, false, ['page' => $page]);

        $this->success('获取成功', $list);
    }

    /**
     * 获取订单详情
     *
     * @ApiMethod (GET)
     * @ApiParams (name="order_id", type="integer", required=true, description="订单ID")
     */
    public function getOrderDetail()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $order_id = $this->request->get('order_id');
        if (!$order_id) {
            $this->error('参数错误');
        }

        $order = Db::name('order')
            ->where('id', $order_id)
            ->where('user_id', $user->id)
            ->find();

        if (!$order) {
            $this->error('订单不存在');
        }

        $this->success('获取成功', $order);
    }
}
