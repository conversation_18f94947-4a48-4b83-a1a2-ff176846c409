<table class="table table-striped">
    <thead>
        <tr>
            <th>{:__('Title')}</th>
            <th>{:__('Content')}</th>
        </tr>
    </thead>
    <tbody>
        {volist name="row" id="vo"  }
        <tr>
            <td>{$key}</td>
            <td style="word-break: break-all;">{$vo|htmlentities}</td>
        </tr>
        {/volist}
        {if $Think.get.dialog}
        <tr>
            <td>回传数据</td>
            <td>
                <div class="input-group">
                    <input name="callback" class="form-control" value="test" />
                    <span class="input-group-btn"><a href="javascript:;" class="btn btn-success btn-callback" >回传数据</a></span>
                </div>
            </td>
        </tr>
        {/if}
    </tbody>
</table>
<div class="hide layer-footer">
    <label class="control-label col-xs-12 col-sm-2"></label>
    <div class="col-xs-12 col-sm-8">
        <button type="reset" class="btn btn-primary btn-embossed btn-close" onclick="Layer.closeAll();">{:__('Close')}</button>
    </div>
</div>
