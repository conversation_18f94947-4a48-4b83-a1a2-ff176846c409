define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'jianghu/index' + location.search,
                    add_url: 'jianghu/add',
                    edit_url: 'jianghu/edit',
                    del_url: 'jianghu/del',
                    multi_url: 'jianghu/multi',
                    import_url: 'jianghu/import',
                    table: 'jianghu',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                  url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                //切换卡片视图和表格视图两种模式
                showToggle: false,
                //显示隐藏列可以快速切换字段列的显示和隐藏
                showColumns: false,
                //导出整个表的所有行导出整个表的所有行
                showExport: false,
                //导出数据格式
                exportTypes: ['excel'],
                //搜索
                search: true,
                //表格上方的搜索搜索指表格上方的搜索
                searchFormVisible: false,
                templateView: true,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id'), operate: '='},
                        {field: 'kind', title: __('Kind'), searchList: {"help":__('Kind help'),"circle":__('Kind circle'),"dynamic":__('Kind dynamic')}, formatter: Table.api.formatter.normal},
                        {field: 'user_id', title: __('User_id'), operate: '='},
                        {field: 'nickname', title: __('Nickname'), operate: 'LIKE'},
                        {field: 'content', title: __('Content'), operate: 'LIKE'},
                        {field: 'address', title: __('Address'), operate: 'LIKE'},
                        {field: 'status', title: __('Status'), searchList: {"pending":"审核中","pass":"已通过","refuse":"已拒绝"}, formatter: Table.api.formatter.normal},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime}
                    ]
                ]
            });

            Template.helper("cdnurl", function (image) {
                return Fast.api.cdnurl(image);
            });

            Template.helper("Moment", Moment);

            // 为表格绑定事件
            Table.api.bindevent(table);

            // 绑定自定义模板中的操作按钮事件
            $(document).on('click', '.btn-detail', function () {
                var id = $(this).data('id');
                Fast.api.open('jianghu/detail?id=' + id, '查看详情', {
                    area: ['90%', '90%']
                });
            });

            $(document).on('click', '.btn-delone', function () {
                var that = this;
                var id = $(this).data('id');
                Layer.confirm(__('Are you sure you want to delete this item?'), {
                    icon: 3,
                    title: __('Warning'),
                    shadeClose: true
                }, function (index) {
                    Fast.api.ajax({
                        url: 'jianghu/del',
                        data: {ids: id},
                        type: "POST"
                    }, function (data, ret) {
                        Layer.close(index);
                        Toastr.success(ret.msg);
                        table.bootstrapTable('refresh');
                    });
                });
            });

            // 快速审核功能
            $(document).on('click', '.btn-audit-quick', function () {
                var id = $(this).data('id');
                var action = $(this).data('action');
                var that = this;

                var title = '';
                var content = '';

                if (action == 'pass') {
                    title = '审核通过';
                    content = '确定要审核通过这条动态吗？';
                } else if (action == 'refuse') {
                    title = '审核拒绝/下架';
                    content = '确定要拒绝/下架这条动态吗？';
                }

                if (action == 'refuse') {
                    // 拒绝需要填写原因
                    Layer.prompt({
                        title: title,
                        formType: 2,
                        value: '',
                        area: ['400px', '200px']
                    }, function(value, index) {
                        Fast.api.ajax({
                            url: 'jianghu/audit',
                            data: {
                                id: id,
                                status: action,
                                refuse_remark: value
                            },
                            type: 'POST'
                        }, function(data, ret) {
                            Layer.close(index);
                            Toastr.success(ret.msg);
                            table.bootstrapTable('refresh');
                        });
                    });
                } else {
                    // 通过直接确认
                    Layer.confirm(content, {
                        icon: 1,
                        title: title
                    }, function(index) {
                        Fast.api.ajax({
                            url: 'jianghu/audit',
                            data: {
                                id: id,
                                status: action,
                                refuse_remark: ''
                            },
                            type: 'POST'
                        }, function(data, ret) {
                            Layer.close(index);
                            Toastr.success(ret.msg);
                            table.bootstrapTable('refresh');
                        });
                    });
                }
            });
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        detail: function () {
            Controller.api.bindevent();

            // 删除评论功能
            $(document).on('click', '.btn-del-comment', function(e) {
                e.preventDefault();
                e.stopPropagation();

                var id = $(this).data('id');
                var that = this;

                if (!id) {
                    Toastr.error('评论ID不能为空');
                    return;
                }

                Layer.confirm('确定要删除这条评论吗？', {
                    icon: 3,
                    title: '警告',
                    shadeClose: true
                }, function(index) {
                    Fast.api.ajax({
                        url: 'jianghu/delcomment',
                        data: {id: id},
                        type: 'POST'
                    }, function(data, ret) {
                        Layer.close(index);
                        Toastr.success(ret.msg);
                        $(that).closest('.comment-item').fadeOut(function() {
                            $(this).remove();
                            // 更新评论数量显示
                            var commentCount = $('.comment-item').length;
                            var $commentTitle = $('.panel-title:contains("评论记录")');
                            if ($commentTitle.length > 0) {
                                $commentTitle.html('<i class="fa fa-comment"></i> 评论记录 (' + commentCount + ')');
                            }
                        });
                    }, function(data, ret) {
                        Layer.close(index);
                        Toastr.error(ret.msg || '删除失败');
                    });
                });
            });

            // 审核功能
            $(document).on('click', '.btn-audit', function(e) {
                e.preventDefault();
                e.stopPropagation();

                var id = $(this).data('id');
                var action = $(this).data('action');
                var that = this;

                var title = '';
                var icon = 3;

                if (action == 'pass') {
                    title = '审核通过';
                    icon = 1;
                } else if (action == 'refuse') {
                    title = '审核拒绝/下架';
                    icon = 2;
                }

                if (action == 'refuse') {
                    // 拒绝需要填写原因
                    Layer.prompt({
                        title: title,
                        formType: 2,
                        value: '',
                        area: ['400px', '200px']
                    }, function(value, index) {
                        Fast.api.ajax({
                            url: 'jianghu/audit',
                            data: {
                                id: id,
                                status: action,
                                refuse_remark: value
                            },
                            type: 'POST'
                        }, function(data, ret) {
                            Layer.close(index);
                            Toastr.success(ret.msg);
                            // 刷新页面
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        }, function(data, ret) {
                            Layer.close(index);
                            Toastr.error(ret.msg || '操作失败');
                        });
                    });
                } else {
                    // 通过直接确认
                    Layer.confirm('确定要审核通过这条动态吗？', {
                        icon: icon,
                        title: title,
                        shadeClose: true
                    }, function(index) {
                        Fast.api.ajax({
                            url: 'jianghu/audit',
                            data: {
                                id: id,
                                status: action,
                                refuse_remark: ''
                            },
                            type: 'POST'
                        }, function(data, ret) {
                            Layer.close(index);
                            Toastr.success(ret.msg);
                            // 刷新页面
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        }, function(data, ret) {
                            Layer.close(index);
                            Toastr.error(ret.msg || '操作失败');
                        });
                    });
                }
            });
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
