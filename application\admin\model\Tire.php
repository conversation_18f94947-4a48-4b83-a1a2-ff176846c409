<?php

namespace app\admin\model;

use think\Model;


class Tire extends Model
{





    // 表名
    protected $name = 'tire';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'status_text',
        'paytime_text',
        'nickname',
        'images_arr',
        'distance',
        'brand_string'
    ];


    protected static function init()
    {
        self::afterInsert(function ($row) {
            if (!$row['weigh']) {
                $pk = $row->getPk();
                $row->getQuery()->where($pk, $row[$pk])->update(['weigh' => $row[$pk]]);
            }
        });
    }


    public function getStatusList()
    {
        return ['pending' => __('Status pending'), 'paid' => __('Status paid')];
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ?: ($data['status'] ?? '');
        $list = $this->getStatusList();
        return $list[$value] ?? '';
    }

    public function getImagesArrAttr($value, $data)
    {
        $value = $value ?: ($data['images'] ?? '');
        return explode(',', $value);    
    }

    public function getPaytimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['paytime'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setPaytimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    public function getNicknameAttr($value, $data)
    {
        $userId = $data['user_id'] ?? 0;
        if (!$userId) {
            return '-';
        }

        $user = \app\admin\model\User::where('id', $userId)->find();
        return $user ? $user->nickname : '-';
    }

    public function getDistanceAttr($value, $data)
    {
        // 这个属性会在控制器中动态设置
        return $value;
    }

    public function getBrandStringAttr($value, $data)
    {
        $brandIds = $data['brand_ids'] ?? '';
        if (empty($brandIds)) {
            return '-';
        }

        $brandIdsArray = explode(',', $brandIds);
        $brands = \app\admin\model\TireBrand::whereIn('id', $brandIdsArray)->column('name');
        return empty($brands) ? '-' : implode('、', $brands);
    }

    public function user()
    {
        return $this->belongsTo('User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    public function company()
    {
        return $this->belongsTo('Company', 'user_id', 'user_id', [], 'LEFT')->setEagerlyType(0);
    }
}
