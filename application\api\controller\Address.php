<?php

namespace app\api\controller;

use app\common\controller\Api;
use think\Db;

/**
 * 地址管理接口
 */
class Address extends Api
{
    protected $noNeedLogin = [];
    protected $noNeedRight = '*';

    /**
     * 添加地址
     *
     * @ApiMethod (POST)
     * @ApiParams (name="kind", type="string", required=true, description="地址类型:get=装货地址,send=卸货地址")
     * @ApiParams (name="username", type="string", required=true, description="联系人")
     * @ApiParams (name="mobile", type="string", required=true, description="电话")
     * @ApiParams (name="address", type="string", required=true, description="所在地址")
     * @ApiParams (name="lng", type="string", required=true, description="经度")
     * @ApiParams (name="lat", type="string", required=true, description="纬度")
     * @ApiParams (name="detail", type="string", required=true, description="详细地址")
     * @ApiParams (name="default", type="int", required=false, description="是否默认:0=否,1=是")
     */
    public function add()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $kind = $this->request->post('kind');
        $username = $this->request->post('username');
        $mobile = $this->request->post('mobile');
        $address = $this->request->post('address');
        $lng = $this->request->post('lng');
        $lat = $this->request->post('lat');
        $detail = $this->request->post('detail');
        $default = $this->request->post('default', 0);

        // 验证必填参数
        if (!$kind || !$username || !$mobile || !$address || !$lng || !$lat || !$detail) {
            $this->error(__('Invalid parameters'));
        }

        // 验证地址类型
        if (!in_array($kind, ['get', 'send'])) {
            $this->error('地址类型参数错误');
        }

        // 验证手机号格式
        if (!preg_match('/^1[3-9]\d{9}$/', $mobile)) {
            $this->error('手机号格式不正确');
        }

        // 如果设置为默认地址，先取消该类型的其他默认地址
        if ($default == 1) {
            Db::name('address')->where('user_id', $user->id)->where('kind', $kind)->update(['default' => 0]);
        }

        $data = [
            'user_id' => $user->id,
            'kind' => $kind,
            'username' => $username,
            'mobile' => $mobile,
            'address' => $address,
            'lng' => $lng,
            'lat' => $lat,
            'detail' => $detail,
            'default' => $default,
            'createtime' => time()
        ];

        try {
            $id = Db::name('address')->insertGetId($data);
        } catch (\Exception $e) {
            $this->error('添加失败，请重试');
        }

        $this->success('地址添加成功', ['id' => $id]);
    }

    /**
     * 删除地址
     *
     * @ApiMethod (POST)
     * @ApiParams (name="id", type="int", required=true, description="地址ID")
     */
    public function delete()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $id = $this->request->post('id');
        if (!$id) {
            $this->error(__('Invalid parameters'));
        }

        // 检查地址是否存在且属于当前用户
        $address = Db::name('address')->where('id', $id)->where('user_id', $user->id)->find();
        if (!$address) {
            $this->error('地址不存在或无权限删除');
        }

        try {
            Db::name('address')->where('id', $id)->delete();
        } catch (\Exception $e) {
            $this->error('删除失败，请重试');
        }

        $this->success('删除成功');
    }

    /**
     * 修改地址
     *
     * @ApiMethod (POST)
     * @ApiParams (name="id", type="int", required=true, description="地址ID")
     * @ApiParams (name="kind", type="string", required=true, description="地址类型:get=装货地址,send=卸货地址")
     * @ApiParams (name="username", type="string", required=true, description="联系人")
     * @ApiParams (name="mobile", type="string", required=true, description="电话")
     * @ApiParams (name="address", type="string", required=true, description="所在地址")
     * @ApiParams (name="lng", type="string", required=true, description="经度")
     * @ApiParams (name="lat", type="string", required=true, description="纬度")
     * @ApiParams (name="detail", type="string", required=true, description="详细地址")
     * @ApiParams (name="default", type="int", required=false, description="是否默认:0=否,1=是")
     */
    public function edit()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $id = $this->request->post('id');
        $kind = $this->request->post('kind');
        $username = $this->request->post('username');
        $mobile = $this->request->post('mobile');
        $address = $this->request->post('address');
        $lng = $this->request->post('lng');
        $lat = $this->request->post('lat');
        $detail = $this->request->post('detail');
        $default = $this->request->post('default', 0);

        // 验证必填参数
        if (!$id || !$kind || !$username || !$mobile || !$address || !$lng || !$lat || !$detail) {
            $this->error(__('Invalid parameters'));
        }

        // 检查地址是否存在且属于当前用户
        $existAddress = Db::name('address')->where('id', $id)->where('user_id', $user->id)->find();
        if (!$existAddress) {
            $this->error('地址不存在或无权限修改');
        }

        // 验证地址类型
        if (!in_array($kind, ['get', 'send'])) {
            $this->error('地址类型参数错误');
        }

        // 验证手机号格式
        if (!preg_match('/^1[3-9]\d{9}$/', $mobile)) {
            $this->error('手机号格式不正确');
        }

        // 如果设置为默认地址，先取消该类型的其他默认地址
        if ($default == 1) {
            Db::name('address')->where('user_id', $user->id)->where('kind', $kind)->where('id', '<>', $id)->update(['default' => 0]);
        }

        $data = [
            'kind' => $kind,
            'username' => $username,
            'mobile' => $mobile,
            'address' => $address,
            'lng' => $lng,
            'lat' => $lat,
            'detail' => $detail,
            'default' => $default
        ];

        try {
            Db::name('address')->where('id', $id)->update($data);
        } catch (\Exception $e) {
            $this->error('修改失败，请重试');
        }

        $this->success('修改成功');
    }

    /**
     * 地址列表
     *
     * @ApiMethod (GET)
     * @ApiParams (name="kind", type="string", required=false, description="地址类型:get=装货地址,send=卸货地址")
     * @ApiParams (name="page", type="int", required=false, description="页码，默认1")
     * @ApiParams (name="limit", type="int", required=false, description="每页数量，默认10")
     */
    public function index()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $kind = $this->request->get('kind', '');
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);

        // 构建查询条件
        $where = ['user_id' => $user->id];
        if ($kind) {
            $where['kind'] = $kind;
        }

        // 查询数据
        $list = Db::name('address')
            ->where($where)
            ->order('default desc, createtime desc')
            ->page($page, $limit)
            ->select();

        // 查询总数
        $total = Db::name('address')->where($where)->count();

        $this->success('获取成功', [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit
        ]);
    }

    /**
     * 获取地址详情
     *
     * @ApiMethod (GET)
     * @ApiParams (name="id", type="int", required=true, description="地址ID")
     */
    public function info()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $id = $this->request->get('id');
        if (!$id) {
            $this->error(__('Invalid parameters'));
        }

        // 查询地址信息
        $address = Db::name('address')->where('id', $id)->where('user_id', $user->id)->find();
        if (!$address) {
            $this->error('地址不存在或无权限查看');
        }

        $this->success('获取成功', $address);
    }

    /**
     * 设置默认地址
     *
     * @ApiMethod (POST)
     * @ApiParams (name="id", type="int", required=true, description="地址ID")
     */
    public function setDefault()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $id = $this->request->post('id');
        if (!$id) {
            $this->error(__('Invalid parameters'));
        }

        // 检查地址是否存在且属于当前用户
        $address = Db::name('address')->where('id', $id)->where('user_id', $user->id)->find();
        if (!$address) {
            $this->error('地址不存在或无权限操作');
        }

        try {
            // 先取消该类型的其他默认地址
            Db::name('address')->where('user_id', $user->id)->where('kind', $address['kind'])->update(['default' => 0]);

            // 设置当前地址为默认
            Db::name('address')->where('id', $id)->update(['default' => 1]);
        } catch (\Exception $e) {
            $this->error('设置失败，请重试');
        }

        $this->success('设置成功');
    }

    /**
     * 获取默认地址
     *
     * @ApiMethod (GET)
     * @ApiParams (name="kind", type="string", required=true, description="地址类型:get=装货地址,send=卸货地址")
     */
    public function getDefault()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $kind = $this->request->get('kind');
        if (!$kind) {
            $this->error(__('Invalid parameters'));
        }

        // 验证地址类型
        if (!in_array($kind, ['get', 'send'])) {
            $this->error('地址类型参数错误');
        }

        // 查询默认地址
        $address = Db::name('address')
            ->where('user_id', $user->id)
            ->where('kind', $kind)
            ->where('default', 1)
            ->find();

        if (!$address) {
            $this->success('获取成功', ['has_default' => false]);
        }

        $this->success('获取成功', [
            'has_default' => true,
            'address' => $address
        ]);
    }
}
