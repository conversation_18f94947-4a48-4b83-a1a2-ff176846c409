<?php

namespace app\admin\model;

use think\Model;

class Address extends Model
{
    // 表名
    protected $name = 'address';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'kind_text'
    ];

    public function getKindList()
    {
        return ['get' => '装货地址', 'send' => '卸货地址'];
    }

    public function getKindTextAttr($value, $data)
    {
        $value = $value ?: ($data['kind'] ?? '');
        $list = $this->getKindList();
        return $list[$value] ?? '';
    }

    public function user()
    {
        return $this->belongsTo('User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
