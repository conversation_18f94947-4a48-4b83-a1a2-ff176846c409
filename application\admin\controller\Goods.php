<?php

namespace app\admin\controller;

use app\common\controller\Backend;

/**
 * 货源信息
 *
 * @icon fa fa-circle-o
 */
class Goods extends Backend
{

    /**
     * Goods模型对象
     * @var \app\admin\model\Goods
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Goods;
        $this->view->assign("carTypeList", $this->model->getCarTypeList());
        $this->view->assign("timeTypeList", $this->model->getTimeTypeList());
        $this->view->assign("statusList", $this->model->getStatusList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams(null,true);

            $list = $this->model
                    ->with(['user', 'getAddress', 'sendAddress', 'goodsCar'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                // 用户信息
                if ($row->user) {
                    $row->getRelation('user')->visible(['id', 'nickname', 'avatar']);
                }

                // 装货地址信息
                if ($row->getAddress) {
                    $row['get_address'] = $row->getAddress->address;
                    $row['get_username'] = $row->getAddress->username;
                    $row['get_mobile'] = $row->getAddress->mobile;
                    $row['get_detail'] = $row->getAddress->detail;
                    $row['get_lng'] = $row->getAddress->lng;
                    $row['get_lat'] = $row->getAddress->lat;
                } else {
                    $row['get_address'] = '';
                    $row['get_username'] = '';
                    $row['get_mobile'] = '';
                    $row['get_detail'] = '';
                    $row['get_lng'] = '';
                    $row['get_lat'] = '';
                }

                // 卸货地址信息
                if ($row->sendAddress) {
                    $row['send_address'] = $row->sendAddress->address;
                    $row['send_username'] = $row->sendAddress->username;
                    $row['send_mobile'] = $row->sendAddress->mobile;
                    $row['send_detail'] = $row->sendAddress->detail;
                    $row['send_lng'] = $row->sendAddress->lng;
                    $row['send_lat'] = $row->sendAddress->lat;
                } else {
                    $row['send_address'] = '';
                    $row['send_username'] = '';
                    $row['send_mobile'] = '';
                    $row['send_detail'] = '';
                    $row['send_lng'] = '';
                    $row['send_lat'] = '';
                }

                // 车型信息
                if ($row->goodsCar) {
                    $row['car_name'] = $row->goodsCar->name;
                } else {
                    $row['car_name'] = '';
                }

                // 格式化金额
                $row['amount'] = number_format($row->amount, 2);

                // 格式化距离
                $row['distance'] = $row->distance ? number_format($row->distance, 2) : '0.00';

                // 格式化时间
                $row['createtime_text'] = date('Y-m-d H:i:s', $row->createtime);
                $row['paytime_text'] = $row->paytime ? date('Y-m-d H:i:s', $row->paytime) : '';

                // 状态文本
                $row['car_type_text'] = $row->car_type == 'all' ? '整车' : '拼车';
                $row['time_type_text'] = $row->time_type == 'normal' ? '常规' : '紧急';
                $row['status_text'] = $this->getStatusText($row->status);
                $row['is_show_text'] = $row->is_show == 'normal' ? '上架' : '下架';
                $row['paytype_text'] = $this->getPaytypeText($row->paytype);

                // 清理关联数据，避免输出过多数据
                unset($row->getAddress, $row->sendAddress, $row->goodsCar);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 获取状态文本
     */
    private function getStatusText($status)
    {
        $statusMap = [
            'pending' => '待支付',
            'audit' => '审核中',
            'pass' => '已通过',
            'refuse' => '已拒绝'
        ];
        return $statusMap[$status] ?? '未知状态';
    }

    /**
     * 获取支付方式文本
     */
    private function getPaytypeText($paytype)
    {
        if (!$paytype) {
            return '';
        }
        $paytypeMap = [
            'wechat' => '微信支付',
            'alipay' => '支付宝',
            'balance' => '余额支付'
        ];
        return $paytypeMap[$paytype] ?? $paytype;
    }

    /**
     * 审核通过
     */
    public function auditPass()
    {
        $id = $this->request->post('id');
        if (!$id) {
            $this->error('参数错误');
        }

        $goods = $this->model->get($id);
        if (!$goods) {
            $this->error('货源信息不存在');
        }

        if ($goods->status !== 'audit') {
            $this->error('只有审核中的货源才能进行审核操作');
        }

        try {
            $goods->status = 'pass';
            $goods->save();
        } catch (\Exception $e) {
            $this->error('操作失败：' . $e->getMessage());
        }

        $this->success('审核通过成功');
    }

    /**
     * 审核拒绝
     */
    public function auditRefuse()
    {
        $id = $this->request->post('id');
        $refuse_remark = $this->request->post('refuse_remark');

        if (!$id || !$refuse_remark) {
            $this->error('参数错误');
        }

        $goods = $this->model->get($id);
        if (!$goods) {
            $this->error('货源信息不存在');
        }

        if ($goods->status !== 'audit') {
            $this->error('只有审核中的货源才能进行审核操作');
        }

        try {
            $goods->status = 'refuse';
            $goods->refuse_remark = $refuse_remark;
            $goods->save();
        } catch (\Exception $e) {
            $this->error('操作失败：' . $e->getMessage());
        }

        $this->success('审核拒绝成功');
    }

    /**
     * 上架/下架
     */
    public function toggleShow()
    {
        $id = $this->request->post('id');
        $is_show = $this->request->post('is_show');

        if (!$id || !$is_show) {
            $this->error('参数错误');
        }

        if (!in_array($is_show, ['normal', 'hidden'])) {
            $this->error('状态参数错误');
        }

        $goods = $this->model->get($id);
        if (!$goods) {
            $this->error('货源信息不存在');
        }

        if ($goods->status !== 'pass') {
            $this->error('只有审核通过的货源才能进行上架/下架操作');
        }

        try {
            $goods->is_show = $is_show;
            $goods->save();
        } catch (\Exception $e) {
            $this->error('操作失败：' . $e->getMessage());
        }

        $action = $is_show === 'normal' ? '上架' : '下架';
        $this->success($action . '成功');
    }

}
