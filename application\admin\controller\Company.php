<?php

namespace app\admin\controller;

use app\common\controller\Backend;

/**
 * 企业审核
 *
 * @icon fa fa-circle-o
 */
class Company extends Backend
{

    /**
     * Company模型对象
     * @var \app\admin\model\Company
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Company;
        $this->view->assign("statusList", $this->model->getStatusList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     *
     * @return string|Json
     * @throws \think\Exception
     * @throws DbException
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if (false === $this->request->isAjax()) {
            return $this->view->fetch();
        }
        //如果发送的来源是 Selectpage，则转发到 Selectpage
        if ($this->request->request('keyField')) {
            return $this->selectpage();
        }
        [$where, $sort, $order, $offset, $limit] = $this->buildparams(null,true);
        $list = $this->model
            ->with(['category','user'])
            ->where($where)
            ->order($sort, $order)
            ->paginate($limit);
        $result = ['total' => $list->total(), 'rows' => $list->items()];
        return json($result);
    }

    /**
     * 审核通过
     */
    public function pass()
    {
        $ids = $this->request->param("ids");
        if (empty($ids)) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }
        $pk = $this->model->getPk();
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            $this->model->where($this->dataLimitField, 'in', $adminIds);
        }
        $list = $this->model->where($pk, 'in', $ids)->select();
        $count = 0;
        \think\Db::startTrans();
        try {
            foreach ($list as $item) {
                if ($item->status == 'pending') {
                    $item->status = 'pass';
                    $item->save();
                    $count++;
                }
            }
            \think\Db::commit();
        } catch (\Exception $e) {
            \think\Db::rollback();
            $this->error($e->getMessage());
        }
        if ($count) {
            $this->success("审核通过成功");
        } else {
            $this->error("没有可审核的记录");
        }
    }

    /**
     * 审核拒绝
     */
    public function refuse()
    {
        $ids = $this->request->param("ids");
        if (empty($ids)) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }
        $pk = $this->model->getPk();
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            $this->model->where($this->dataLimitField, 'in', $adminIds);
        }
        $list = $this->model->where($pk, 'in', $ids)->select();
        $count = 0;
        \think\Db::startTrans();
        try {
            foreach ($list as $item) {
                if ($item->status == 'pending') {
                    $item->status = 'refuse';
                    $item->save();
                    $count++;
                }
            }
            \think\Db::commit();
        } catch (\Exception $e) {
            \think\Db::rollback();
            $this->error($e->getMessage());
        }
        if ($count) {
            $this->success("审核拒绝成功");
        } else {
            $this->error("没有可审核的记录");
        }
    }
}
