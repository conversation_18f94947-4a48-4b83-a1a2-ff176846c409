<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <h4 class="panel-title">
            <i class="fa fa-eye"></i> 江湖动态详情
        </h4>
    </div>

    <div class="panel-body">
        <!-- 基本信息 -->
        <div class="row">
            <div class="col-md-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h5 class="panel-title">
                            <i class="fa fa-info-circle"></i> 基本信息
                            <!-- 类型标识 -->
                            {if $jianghu.kind == 'help'}
                            <span class="label label-warning pull-right">
                                <i class="fa fa-question-circle"></i> 求助
                            </span>
                            {elseif $jianghu.kind == 'circle'}
                            <span class="label label-info pull-right">
                                <i class="fa fa-users"></i> 圈子
                            </span>
                            {else}
                            <span class="label label-success pull-right">
                                <i class="fa fa-rss"></i> 动态
                            </span>
                            {/if}
                        </h5>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-8">
                                <table class="table table-bordered">
                                    <tr>
                                        <td width="120"><strong>ID</strong></td>
                                        <td>{$jianghu.id}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>类型</strong></td>
                                        <td>{$jianghu.kind_text}</td>
                                    </tr>
                                    {if $category}
                                    <tr>
                                        <td><strong>求助分类</strong></td>
                                        <td>{$category.name}</td>
                                    </tr>
                                    {/if}
                                    <tr>
                                        <td><strong>发布者</strong></td>
                                        <td>
                                            {if $jianghu.avatar}
                                            <img src="{$jianghu.avatar}" class="img-circle" width="30" height="30">
                                            {/if}
                                            {$jianghu.nickname|default='匿名用户'} (ID: {$jianghu.user_id})
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>发布时间</strong></td>
                                        <td>{$jianghu.createtime|date='Y-m-d H:i:s',###}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>地址</strong></td>
                                        <td>{$jianghu.address|default='未填写'}</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-4">
                                <div class="panel panel-info">
                                    <div class="panel-heading">
                                        <h6 class="panel-title">统计信息</h6>
                                    </div>
                                    <div class="panel-body">
                                        <div class="stat-item">
                                            <i class="fa fa-eye text-primary"></i>
                                            <strong>浏览量:</strong> {$jianghu.show|default=0}
                                        </div>
                                        <div class="stat-item">
                                            <i class="fa fa-share text-success"></i>
                                            <strong>分享数:</strong> {$jianghu.share|default=0}
                                        </div>
                                        <div class="stat-item">
                                            <i class="fa fa-thumbs-up text-warning"></i>
                                            <strong>点赞数:</strong> {$clicks|count}
                                        </div>
                                        <div class="stat-item">
                                            <i class="fa fa-comment text-info"></i>
                                            <strong>评论数:</strong> {$comments|count}
                                        </div>
                                    </div>
                                </div>

                                <!-- 审核状态 -->
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h6 class="panel-title">审核状态</h6>
                                    </div>
                                    <div class="panel-body">
                                        {if $jianghu.status == 'pass'}
                                        <span class="label label-success">已通过</span>
                                        {elseif $jianghu.status == 'refuse'}
                                        <span class="label label-danger">已拒绝</span>
                                        {if $jianghu.refuse_remark}
                                        <div class="mt-10">
                                            <strong>拒绝原因:</strong><br>
                                            <span class="text-danger">{$jianghu.refuse_remark}</span>
                                        </div>
                                        {/if}
                                        {else}
                                        <span class="label label-warning">审核中</span>
                                        {/if}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 内容 -->
                        <div class="row">
                            <div class="col-md-12">
                                <h5><i class="fa fa-file-text-o"></i> 内容</h5>
                                <div class="well">
                                    {$jianghu.content|nl2br|default='无内容'}
                                </div>
                            </div>
                        </div>

                        <!-- 图片/视频 -->
                        {if $jianghu.images}
                        <div class="row">
                            <div class="col-md-12">
                                <h5><i class="fa fa-image"></i> 图片/视频</h5>
                                <div class="images-container">
                                    {php}
                                    $images = explode(',', $jianghu['images']);
                                    foreach($images as $image) {
                                        if(trim($image)) {
                                            echo '<img src="'.trim($image).'" class="img-thumbnail" style="max-width: 200px; margin: 5px;">';
                                        }
                                    }
                                    {/php}
                                </div>
                            </div>
                        </div>
                        {/if}
                    </div>
                </div>
            </div>
        </div>

        <!-- 点赞记录 -->
        <div class="row">
            <div class="col-md-6">
                <div class="panel panel-warning">
                    <div class="panel-heading">
                        <h5 class="panel-title">
                            <i class="fa fa-thumbs-up"></i> 点赞记录 ({$clicks|count})
                        </h5>
                    </div>
                    <div class="panel-body" style="max-height: 300px; overflow-y: auto;">
                        {if $clicks}
                        <table class="table table-striped table-condensed">
                            <thead>
                                <tr>
                                    <th>用户</th>
                                    <th>时间</th>
                                </tr>
                            </thead>
                            <tbody>
                                {foreach $clicks as $click}
                                <tr>
                                    <td>
                                        {if $click.avatar}
                                        <img src="{$click.avatar}" class="img-circle" width="20" height="20">
                                        {/if}
                                        {$click.nickname|default='匿名用户'} (ID: {$click.user_id})
                                    </td>
                                    <td>{$click.createtime|date='Y-m-d H:i:s',###}</td>
                                </tr>
                                {/foreach}
                            </tbody>
                        </table>
                        {else}
                        <p class="text-muted">暂无点赞记录</p>
                        {/if}
                    </div>
                </div>
            </div>

            <!-- 评论记录 -->
            <div class="col-md-6">
                <div class="panel panel-info">
                    <div class="panel-heading">
                        <h5 class="panel-title">
                            <i class="fa fa-comment"></i> 评论记录 ({$comments|count})
                        </h5>
                    </div>
                    <div class="panel-body" style="max-height: 300px; overflow-y: auto;">
                        {if $comments}
                        {foreach $comments as $comment}
                        <div class="comment-item" style="border-bottom: 1px solid #eee; padding: 10px 0;">
                            <div class="comment-header">
                                {if $comment.avatar}
                                <img src="{$comment.avatar}" class="img-circle" width="25" height="25">
                                {/if}
                                <strong>{$comment.nickname|default='匿名用户'}</strong>
                                <small class="text-muted">({$comment.createtime|date='Y-m-d H:i:s',###})</small>
                                <a href="javascript:;" class="btn btn-xs btn-danger pull-right btn-del-comment"
                                   data-id="{$comment.id}" title="删除评论">
                                    <i class="fa fa-trash"></i>
                                </a>
                            </div>
                            <div class="comment-content" style="margin-top: 5px;">
                                {$comment.content|nl2br}
                            </div>
                        </div>
                        {/foreach}
                        {else}
                        <p class="text-muted">暂无评论</p>
                        {/if}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stat-item {
    margin-bottom: 8px;
}
.stat-item i {
    width: 20px;
}
.mt-10 {
    margin-top: 10px;
}
.comment-item:last-child {
    border-bottom: none !important;
}
</style>


