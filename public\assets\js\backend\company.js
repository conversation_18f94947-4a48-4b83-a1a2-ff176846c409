define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'company/index' + location.search,
                    add_url: 'company/add',
                    edit_url: 'company/edit',
                    del_url: 'company/del',
                    multi_url: 'company/multi',
                    import_url: 'company/import',
                    table: 'company',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'user.nickname', title: __('User_id')},
                        {field: 'category.name', title: __('企业类别')},
                        {field: 'license_image', title: __('License_image'), operate: false, events: Table.api.events.image, formatter: Table.api.formatter.image},
                        {field: 'name', title: __('Name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'code', title: __('Code'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'legal_name', title: __('Legal_name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'legal_card', title: __('Legal_card'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'front_image', title: __('Front_image'), operate: false, events: Table.api.events.image, formatter: Table.api.formatter.image},
                        {field: 'reverse_image', title: __('Reverse_image'), operate: false, events: Table.api.events.image, formatter: Table.api.formatter.image},
                        {field: 'status', title: __('Status'), searchList: {"pending":__('Status pending'),"pass":__('Status pass'),"refuse":__('Status refuse')}, formatter: Table.api.formatter.status},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {
                            field: 'operate',
                            width: "180px",
                            title: __('Operate'),
                            table: table,
                            events: Table.api.events.operate,
                            buttons: [
                                {
                                    name: 'pass',
                                    title: __('审核通过'),
                                    text: __('通过'),
                                    classname: 'btn btn-xs btn-success btn-ajax',
                                    extend: 'data-toggle="tooltip"',
                                    icon: 'fa fa-check',
                                    url: 'company/pass',
                                    confirm: '确认审核通过该企业？',
                                    success: function (data, ret) {
                                        $("#table").bootstrapTable('refresh');
                                    },
                                    visible: function (row) {
                                        return row.status == 'pending';
                                    }
                                },
                                {
                                    name: 'refuse',
                                    title: __('审核拒绝'),
                                    text: __('拒绝'),
                                    classname: 'btn btn-xs btn-danger btn-ajax',
                                    extend: 'data-toggle="tooltip"',
                                    icon: 'fa fa-times',
                                    url: 'company/refuse',
                                    confirm: '确认拒绝该企业审核？',
                                    success: function (data, ret) {
                                        $("#table").bootstrapTable('refresh');
                                    },
                                    visible: function (row) {
                                        return row.status == 'pending';
                                    }
                                }
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
