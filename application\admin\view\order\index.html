<div class="panel panel-default panel-intro">

    <div class="panel-heading">
        {:build_heading(null,FALSE)}
        <ul class="nav nav-tabs" data-field="status">
            <li class="{:$Think.get.status === null ? 'active' : ''}"><a href="#t-all" data-value=""
                    data-toggle="tab">{:__('All')}</a></li>
            {foreach name="statusList" item="vo"}
            <li class="{:$Think.get.status === (string)$key ? 'active' : ''}"><a href="#t-{$key}" data-value="{$key}"
                    data-toggle="tab">{$vo}</a></li>
            {/foreach}
        </ul>
    </div>

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}"><i
                                class="fa fa-refresh"></i> </a>
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                        data-operate-edit="{:$auth->check('order/edit')}"
                        data-operate-del="{:$auth->check('order/del')}" width="100%">
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script type="text/html" id="itemtpl">
        <% if(i == 0){ %>
            <div class="order-head">
                <div class="col-sm-3 text-center obr flex">订单信息</div>
                <div class="col-sm-2 text-center obr">用户信息</div>
                <div class="col-sm-3 text-center obr">业务信息</div>
                <div class="col-sm-2 text-center obr">支付信息</div>
                <div class="col-sm-2 text-center">支付状态</div>
            </div>
        <% } %>

        <div class="col-sm-12 mt-15">
            <div class="item-top">
                <span>订单号：<%=item.orderid%></span>
                <span style="margin-left:100px;">创建时间：<%=Moment(item.createtime*1000).format("YYYY-MM-DD HH:mm:ss")%></span>
                <% if(item.paytime){ %>
                <span style="margin-left:100px;">支付时间：<%=Moment(item.paytime*1000).format("YYYY-MM-DD HH:mm:ss")%></span>
                <% } %>
            </div>
            <div class="item-content">
                <!-- 订单信息 col-sm-3 -->
                <div class="col-sm-3 vhc obr" style="padding: 10px;">
                    <div class="order-info">
                        <div class="order-id">
                            <strong>订单ID: <%=item.id%></strong>
                        </div>
                        <div class="order-details" style="margin-top: 5px; font-size: 12px;">
                            <div>企业分类ID：<%=item.company_category_id%></div>
                            <div>关联ID：<%=item.link_id%></div>
                            <% if(item.transid){ %>
                            <div class="text-muted">流水号：<%=item.transid%></div>
                            <% } %>
                        </div>
                    </div>
                </div>

                <!-- 用户信息 col-sm-2 -->
                <div class="col-sm-2 vhc obr flex">
                    <div class="user-info">
                        <div><strong><%=item.user ? item.user.nickname : '未知用户'%></strong></div>
                        <div class="text-muted">用户ID：<%=item.user_id%></div>
                    </div>
                </div>

                <!-- 业务信息 col-sm-3 -->
                <div class="col-sm-3 vhc obr flex">
                    <div class="business-info">
                        <div><strong><%=item.company_category_name || '未知分类'%></strong></div>
                        <% if(item.link_name){ %>
                        <div class="text-success"><i class="fa fa-link"></i> <%=item.link_name%></div>
                        <% } else { %>
                        <div class="text-info">关联业务ID：<%=item.link_id%></div>
                        <% } %>
                    </div>
                </div>

                <!-- 支付信息 col-sm-2 -->
                <div class="col-sm-2 vhc obr flex">
                    <div class="payment-info text-center">
                        <div><strong>¥<%=item.amount || '0.00'%></strong></div>
                        <% if(item.paytype_text){ %>
                        <div><small class="text-muted"><%=item.paytype_text%></small></div>
                        <% } %>
                    </div>
                </div>

                <!-- 支付状态 col-sm-2 -->
                <div class="col-sm-2 vhc flex">
                    <span class="label <%=item.status == 'paid' ? 'label-success' : 'label-default'%>">
                        <%=item.status_text || (item.status == 'paid' ? '已支付' : '待支付')%>
                    </span>
                </div>
            </div>
        </div>
    </script>

    <style>
        .flex {
            display: flex;
            flex-wrap: wrap;
        }

        .mt-15 {
            margin-top: 15px;
        }

        .item-goods {
            border-bottom: solid 1px #e6e6e6;
            padding: 15px;
        }

        .item-goods:last-child {
            border: none;
        }

        .ctr {
            text-align: right;
            line-height: 35px;
            font-weight: bold;
        }

        .vhc {
            padding: 15px;
            height: 100%;
            align-items: center;
            justify-content: center;
        }

        .order-head {
            background: #f7f7f7;
            padding: 0px 15px;
            width: 100%;
            font-weight: bold;
            margin-top: 5px;
        }

        .order-head div {
            padding: 15px;
        }

        .item-top {
            background: #f7f7f7;
            padding: 10px;
            width: 100%;
            border: 1px solid #e6e6e6;
        }

        .item-content {
            border: 1px solid #e6e6e6;
            border-top: none;
        }

        .obr {
            border-right: 1px solid #e6e6e6;
        }

        /*.fixed-table-toolbar{display: none;}*/
    </style>

</div>
