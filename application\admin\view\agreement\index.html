<style type="text/css">
    @media (max-width: 375px) {
        .edit-form tr td input {
            width: 100%;
        }

        .edit-form tr th:first-child, .edit-form tr td:first-child {
            width: 20%;
        }

        .edit-form tr th:nth-last-of-type(-n+2), .edit-form tr td:nth-last-of-type(-n+2) {
            display: none;
        }
    }
</style>
<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        {:build_heading(null, false)}
        <ul class="nav nav-tabs">
            <li class="active"><a href="#tab-agreement" data-toggle="tab">协议配置</a></li>
        </ul>
    </div>

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="tab-agreement">
                <div class="widget-body no-padding">
                    <form id="agreement-form" class="edit-form form-horizontal" role="form" data-toggle="validator" method="POST" action="{:url('agreement/edit')}">
                        {:token()}
                        <table class="table table-striped">
                            <thead>
                            <tr>
                                <th width="15%">{:__('Title')}</th>
                                <th width="85%">{:__('Content')}</th>
                            </tr>
                            </thead>
                            <tbody>
                            {foreach $agreementList as $item}
                            <tr>
                                <td>{$item.title|htmlentities}</td>
                                <td>
                                    <div class="row">
                                        <div class="col-sm-12 col-xs-12">
                                            <textarea name="row[{$item.id}]" class="form-control editor" rows="10" data-tip="请输入协议内容">{$item.content|htmlentities}</textarea>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {/foreach}
                            </tbody>
                            <tfoot>
                            <tr>
                                <td></td>
                                <td>
                                    <div class="layer-footer">
                                        <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
                                        <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
                                    </div>
                                </td>
                            </tr>
                            </tfoot>
                        </table>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
