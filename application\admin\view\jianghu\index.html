<div class="panel panel-default panel-intro">
    {:build_heading()}

    <!-- 类型标签页 -->
    <div class="panel-heading">
        <ul class="nav nav-tabs" data-field="kind">
            <li class="{:$Think.get.kind === null ? 'active' : ''}">
                <a href="#t-all" data-value="" data-toggle="tab">{:__('All')}</a>
            </li>
            <li class="{:$Think.get.kind === 'help' ? 'active' : ''}">
                <a href="#t-help" data-value="help" data-toggle="tab">
                    <i class="fa fa-question-circle text-warning"></i> {:__('Kind help')}
                </a>
            </li>
            <li class="{:$Think.get.kind === 'circle' ? 'active' : ''}">
                <a href="#t-circle" data-value="circle" data-toggle="tab">
                    <i class="fa fa-users text-info"></i> {:__('Kind circle')}
                </a>
            </li>
            <li class="{:$Think.get.kind === 'dynamic' ? 'active' : ''}">
                <a href="#t-dynamic" data-value="dynamic" data-toggle="tab">
                    <i class="fa fa-rss text-success"></i> {:__('Kind dynamic')}
                </a>
            </li>
        </ul>
    </div>

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}" ><i class="fa fa-refresh"></i> </a>
                        <!-- <a href="javascript:;" class="btn btn-success btn-add {:$auth->check('jianghu/add')?'':'hide'}" title="{:__('Add')}" ><i class="fa fa-plus"></i> {:__('Add')}</a>
                        <a href="javascript:;" class="btn btn-danger btn-del btn-disabled disabled {:$auth->check('jianghu/del')?'':'hide'}" title="{:__('Delete')}" ><i class="fa fa-trash"></i> {:__('Delete')}</a> -->
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                           data-operate-del="{:$auth->check('jianghu/del')}"
                           data-template="#itemtpl"
                           width="100%">
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 自定义模板 -->
    <script type="text/html" id="itemtpl">
        <% if(i == 0){ %>
            <div class="jianghu-head">
                <div class="col-sm-1 text-center obr">类型</div>
                <div class="col-sm-3 text-center obr">内容信息</div>
                <div class="col-sm-2 text-center obr">发布者</div>
                <div class="col-sm-2 text-center obr">统计信息</div>
                <div class="col-sm-2 text-center obr">状态</div>
                <div class="col-sm-2 text-center">操作</div>
            </div>
        <% } %>

        <div class="col-sm-12 mt-15">
            <div class="item-content">
                <!-- 类型标识 col-sm-1 -->
                <div class="col-sm-1 vhc obr" style="min-height: 80px;">
                    <div class="kind-badge text-center">
                        <% if(item.kind == 'help'){ %>
                        <div class="kind-label kind-help">
                            <i class="fa fa-question-circle"></i>
                            <div class="kind-text">求助</div>
                        </div>
                        <% } else if(item.kind == 'circle'){ %>
                        <div class="kind-label kind-circle">
                            <i class="fa fa-users"></i>
                            <div class="kind-text">圈子</div>
                        </div>
                        <% } else if(item.kind == 'dynamic'){ %>
                        <div class="kind-label kind-dynamic">
                            <i class="fa fa-rss"></i>
                            <div class="kind-text">动态</div>
                        </div>
                        <% } %>
                    </div>
                </div>

                <!-- 内容信息 col-sm-3 -->
                <div class="col-sm-3 vhc obr">
                    <div class="content-info">
                        <div class="content-text">
                            <% if(item.content && item.content.length > 50){ %>
                            <%=item.content.substring(0, 50)%>...
                            <% } else { %>
                            <%=item.content || '无内容'%>
                            <% } %>
                        </div>
                        <% if(item.images){ %>
                        <div class="content-images mt-5">
                            <small class="text-muted"><i class="fa fa-image"></i> 包含图片</small>
                        </div>
                        <% } %>
                        <% if(item.address){ %>
                        <div class="content-location mt-5">
                            <small class="text-info"><i class="fa fa-map-marker"></i> <%=item.address%></small>
                        </div>
                        <% } %>
                    </div>
                </div>

                <!-- 发布者信息 col-sm-2 -->
                <div class="col-sm-2 vhc obr">
                    <div class="user-info text-center">
                        <% if(item.avatar){ %>
                        <img src="<%=item.avatar%>" class="user-avatar" alt="头像">
                        <% } else { %>
                        <div class="user-avatar-placeholder"><i class="fa fa-user"></i></div>
                        <% } %>
                        <div class="user-name"><%=item.nickname || '匿名用户'%></div>
                        <div class="user-id text-muted">ID: <%=item.user_id%></div>
                    </div>
                </div>

                <!-- 统计信息 col-sm-2 -->
                <div class="col-sm-2 vhc obr">
                    <div class="stats-info">
                        <div class="stat-item">
                            <i class="fa fa-eye text-primary"></i>
                            <span>浏览: <%=item.show || 0%></span>
                        </div>
                        <div class="stat-item">
                            <i class="fa fa-share text-success"></i>
                            <span>分享: <%=item.share || 0%></span>
                        </div>
                        <div class="stat-item">
                            <i class="fa fa-clock-o text-muted"></i>
                            <span><%=Moment(item.createtime*1000).format("MM-DD HH:mm")%></span>
                        </div>
                    </div>
                </div>

                <!-- 状态信息 col-sm-2 -->
                <div class="col-sm-2 vhc obr">
                    <div class="status-info text-center">
                        <% if(item.status == 'pass'){ %>
                        <span class="label label-success">已通过</span>
                        <% } else if(item.status == 'refuse'){ %>
                        <span class="label label-danger">已拒绝</span>
                        <% if(item.refuse_remark){ %>
                        <div class="refuse-reason mt-5">
                            <small class="text-danger"><%=item.refuse_remark%></small>
                        </div>
                        <% } %>
                        <% } else { %>
                        <span class="label label-warning">审核中</span>
                        <% } %>
                    </div>
                </div>

                <!-- 操作按钮 col-sm-2 -->
                <div class="col-sm-2 vhc">
                    <div class="operate text-center">
                        <a href="javascript:;" class="btn btn-xs btn-primary btn-detail" data-id="<%=item.id%>" title="查看详情">
                            <i class="fa fa-eye"></i> 详情
                        </a>
                        <% if(item.status == 'pending'){ %>
                        <a href="javascript:;" class="btn btn-xs btn-success btn-audit-quick" data-id="<%=item.id%>" data-action="pass" title="审核通过">
                            <i class="fa fa-check"></i> 通过
                        </a>
                        <a href="javascript:;" class="btn btn-xs btn-warning btn-audit-quick" data-id="<%=item.id%>" data-action="refuse" title="审核拒绝">
                            <i class="fa fa-times"></i> 拒绝
                        </a>
                        <% } else if(item.status == 'pass'){ %>
                        <a href="javascript:;" class="btn btn-xs btn-warning btn-audit-quick" data-id="<%=item.id%>" data-action="refuse" title="下架">
                            <i class="fa fa-ban"></i> 下架
                        </a>
                        <% } else if(item.status == 'refuse'){ %>
                        <a href="javascript:;" class="btn btn-xs btn-success btn-audit-quick" data-id="<%=item.id%>" data-action="pass" title="重新上架">
                            <i class="fa fa-check"></i> 上架
                        </a>
                        <% } %>
                        {:$auth->check('jianghu/del') ? '<a href="javascript:;" class="btn btn-xs btn-danger btn-delone" data-id="<%=item.id%>" title="删除"><i class="fa fa-trash"></i> 删除</a>' : ''}
                    </div>
                </div>
            </div>
        </div>
    </script>

    <style>
        .flex {
            display: flex;
            flex-wrap: wrap;
        }

        .mt-15 {
            margin-top: 15px;
        }

        .mt-5 {
            margin-top: 5px;
        }

        .vhc {
            padding: 15px;
            height: 100%;
            align-items: center;
            justify-content: center;
        }

        .jianghu-head {
            background: #f7f7f7;
            padding: 0px 15px;
            width: 100%;
            font-weight: bold;
            margin-top: 5px;
        }

        .jianghu-head div {
            padding: 15px;
        }

        .item-content {
            border: 1px solid #e6e6e6;
            border-top: none;
        }

        .obr {
            border-right: 1px solid #e6e6e6;
        }

        .kind-badge {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
        }

        .kind-label {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 10px 8px;
            border-radius: 6px;
            min-width: 50px;
            min-height: 60px;
            color: white;
            font-weight: bold;
        }

        .kind-label i {
            font-size: 18px;
            margin-bottom: 4px;
        }

        .kind-text {
            font-size: 11px;
            line-height: 1;
        }

        .kind-help {
            background: #f0ad4e;
            border: 1px solid #eea236;
        }

        .kind-circle {
            background: #5bc0de;
            border: 1px solid #46b8da;
        }

        .kind-dynamic {
            background: #5cb85c;
            border: 1px solid #4cae4c;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-bottom: 5px;
        }

        .user-avatar-placeholder {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 5px;
            color: #999;
        }

        .user-name {
            font-weight: bold;
            font-size: 12px;
        }

        .user-id {
            font-size: 11px;
        }

        .stats-info .stat-item {
            margin-bottom: 3px;
            font-size: 12px;
        }

        .stats-info .stat-item i {
            width: 16px;
            text-align: center;
        }

        .content-text {
            font-size: 13px;
            line-height: 1.4;
            margin-bottom: 5px;
        }

        .operate .btn {
            margin: 2px;
        }

        .operate .btn-xs {
            padding: 3px 8px;
            font-size: 11px;
        }

        .refuse-reason {
            font-size: 10px;
            max-width: 120px;
            word-wrap: break-word;
        }

        /*.fixed-table-toolbar{display: none;}*/
    </style>
</div>
