define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 绑定表单事件
            Controller.api.bindevent();
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                // 绑定表单验证和提交事件
                Form.api.bindevent($("form[role=form]"));

                // 监听表单提交
                $(document).on('submit', '#agreement-form', function(e) {
                    e.preventDefault();
                    var form = $(this);
                    var formData = form.serialize();

                    Fast.api.ajax({
                        url: form.attr('action'),
                        data: formData,
                        type: 'POST'
                    }, function(data, ret) {
                        Layer.msg('保存成功');
                        // 重置表单状态
                        form.find('.btn-primary').removeClass('disabled');
                    }, function(data, ret) {
                        Layer.msg(ret.msg || '保存失败');
                    });

                    return false;
                });

                // 监听表单内容变化，启用保存按钮
                $(document).on('input change', '#agreement-form textarea', function() {
                    $('#agreement-form .btn-primary').removeClass('disabled');
                });

                // 监听重置按钮
                $(document).on('click', '#agreement-form button[type="reset"]', function() {
                    setTimeout(function() {
                        $('#agreement-form .btn-primary').addClass('disabled');
                    }, 100);
                });
            }
        }
    };
    return Controller;
});
