<?php

namespace app\admin\controller;

use app\admin\model\User;
use app\common\controller\Backend;
use think\Db;

/**
 * 控制台
 *
 * @icon   fa fa-dashboard
 * @remark 用于展示当前系统中的统计数据、统计报表及重要实时数据
 */
class Dashboard extends Backend
{

    /**
     * 查看
     */
    public function index()
    {
        try {
            \think\Db::execute("SET @@sql_mode='';");
        } catch (\Exception $e) {
        }

        // 订单统计 - 添加错误处理
        try {
            $totalOrder = Db::name('order')->count();
            $totalVipOrder = Db::name('vip_order')->count();
            $totalGoods = Db::name('goods')->count();
            $todayOrder = Db::name('order')->whereTime('createtime', 'today')->count();
            $todayVipOrder = Db::name('vip_order')->whereTime('createtime', 'today')->count();
            $todayGoods = Db::name('goods')->whereTime('createtime', 'today')->count();
        } catch (\Exception $e) {
            // 如果表不存在，设置默认值
            $totalOrder = 0;
            $totalVipOrder = 0;
            $totalGoods = 0;
            $todayOrder = 0;
            $todayVipOrder = 0;
            $todayGoods = 0;
        }

        // 收入统计 - 添加错误处理
        try {
            $todayIncome = Db::name('order')->where('status', 'paid')->whereTime('createtime', 'today')->sum('amount') +
                Db::name('vip_order')->where('status', 'paid')->whereTime('createtime', 'today')->sum('vip_price');
            $totalIncome = Db::name('order')->where('status', 'paid')->sum('amount') +
                Db::name('vip_order')->where('status', 'paid')->sum('vip_price');
        } catch (\Exception $e) {
            $todayIncome = 0;
            $totalIncome = 0;
        }

        // 审核统计 - 添加错误处理
        try {
            $pendingCompany = Db::name('company')->where('status', 'pending')->count();
            $pendingHireCompany = Db::name('hire_company')->where('status', 'pending')->count();
            $pendingGoods = Db::name('goods')->where('status', 'audit')->count();

            // 司机认证统计 - 统计各个认证表中待审核的数量
            $pendingDriverCard = Db::name('driver_card')->where('status', 'pending')->count();
            $pendingDriverLicense = Db::name('driver_license')->where('status', 'pending')->count();
            $pendingDriverDriving = Db::name('driver_driving')->where('status', 'pending')->count();
            $pendingDriverWork = Db::name('driver_work')->where('status', 'pending')->count();
            $pendingDriverCar = Db::name('driver_car')->where('status', 'pending')->count();
            $pendingDriver = $pendingDriverCard + $pendingDriverLicense + $pendingDriverDriving + $pendingDriverWork + $pendingDriverCar;

            $pendingJianghu = Db::name('jianghu')->where('status', 'pending')->count();
            $pendingGoodsAudit = Db::name('goods_audit')->where('status', 'pending')->count();
            $pendingOrder = Db::name('order')->where('status', 'pending')->count();
            $pendingVipOrder = Db::name('vip_order')->where('status', 'pending')->count();
        } catch (\Exception $e) {
            // 如果表不存在，设置默认值
            $pendingCompany = 0;
            $pendingHireCompany = 0;
            $pendingGoods = 0;
            $pendingDriver = 0;
            $pendingJianghu = 0;
            $pendingGoodsAudit = 0;
            $pendingOrder = 0;
            $pendingVipOrder = 0;
        }

        $this->view->assign([
            'totaluser'         => User::count(),
            'totalorder'        => $totalOrder,
            'totalviporder'     => $totalVipOrder,
            'totalgoods'        => $totalGoods,
            'todayusersignup'   => User::whereTime('jointime', 'today')->count(),
            'todayorder'        => $todayOrder,
            'todayviporder'     => $todayVipOrder,
            'todaygoods'        => $todayGoods,
            'todayincome'       => number_format($todayIncome, 2),
            'totalincome'       => number_format($totalIncome, 2),
            'pendingcompany'    => $pendingCompany,
            'pendinghirecompany' => $pendingHireCompany,
            'pendinggoods'      => $pendingGoods,
            'pendingdriver'     => $pendingDriver,
            'pendingjianghu'    => $pendingJianghu,
            'pendinggoodsaudit' => $pendingGoodsAudit,
            'pendingorder'      => $pendingOrder,
            'pendingviporder'   => $pendingVipOrder,
        ]);

        return $this->view->fetch();
    }

    /**
     * 获取图表数据 - AJAX接口
     */
    public function getChartData()
    {
        try {
            $chartData = $this->prepareChartData();
        } catch (\Exception $e) {
            $this->error('获取失败: ' . $e->getMessage());
        }

        $this->success('获取成功', null, $chartData);
    }

    /**
     * 准备图表数据
     */
    private function prepareChartData()
    {
        // 获取最近7天的日期
        $dates = [];
        $userData = [];

        for ($i = 6; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-{$i} days"));
            $dates[] = date('m-d', strtotime($date));

            // 获取当天注册用户数
            try {
                $count = User::whereTime('jointime', 'between', [$date . ' 00:00:00', $date . ' 23:59:59'])->count();
                $userData[] = $count;
            } catch (\Exception $e) {
                $userData[] = 0;
            }
        }

        return [
            'column' => $dates,
            'userdata' => $userData
        ];
    }
}
