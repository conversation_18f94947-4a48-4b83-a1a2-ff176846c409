<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('用户昵称')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-rule="required" disabled data-source="user/user/index" data-field="nickname"
                class="form-control selectpage" name="row[user_id]" type="text" value="{$row.user_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Kind')}:</label>
        <div class="col-xs-12 col-sm-8">

            <select id="c-kind" data-rule="required" class="form-control selectpicker" disabled name="row[kind]">
                {foreach name="kindList" item="vo"}
                <option value="{$key}" {in name="key" value="$row.kind" }selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group" data-favisible="kind=company">
        <label class="control-label col-xs-12 col-sm-2">{:__('License_image')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-license_image" class="form-control" size="50" name="row[license_image]" type="text"
                    value="{$row.license_image|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-license_image" class="btn btn-danger faupload"
                            data-input-id="c-license_image"
                            data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp"
                            data-multiple="false" data-preview-id="p-license_image"><i class="fa fa-upload"></i>
                            {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-license_image" class="btn btn-primary fachoose"
                            data-input-id="c-license_image" data-mimetype="image/*" data-multiple="false"><i
                                class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-license_image"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-license_image"></ul>
        </div>
    </div>
    <div class="form-group" data-favisible="kind=company">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" class="form-control" name="row[name]" type="text" value="{$row.name|htmlentities}">
        </div>
    </div>
    <div class="form-group" data-favisible="kind=company">
        <label class="control-label col-xs-12 col-sm-2">{:__('Code')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-code" class="form-control" name="row[code]" type="text" value="{$row.code|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Realname')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-realname" class="form-control" name="row[realname]" type="text"
                value="{$row.realname|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Idcard')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-idcard" class="form-control" name="row[idcard]" type="text" value="{$row.idcard|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Font_image')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-font_image" class="form-control" size="50" name="row[font_image]" type="text"
                    value="{$row.font_image|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-font_image" class="btn btn-danger faupload"
                            data-input-id="c-font_image"
                            data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp"
                            data-multiple="false" data-preview-id="p-font_image"><i class="fa fa-upload"></i>
                            {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-font_image" class="btn btn-primary fachoose"
                            data-input-id="c-font_image" data-mimetype="image/*" data-multiple="false"><i
                                class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-font_image"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-font_image"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Reverse_image')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-reverse_image" class="form-control" size="50" name="row[reverse_image]" type="text"
                    value="{$row.reverse_image|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-reverse_image" class="btn btn-danger faupload"
                            data-input-id="c-reverse_image"
                            data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp"
                            data-multiple="false" data-preview-id="p-reverse_image"><i class="fa fa-upload"></i>
                            {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-reverse_image" class="btn btn-primary fachoose"
                            data-input-id="c-reverse_image" data-mimetype="image/*" data-multiple="false"><i
                                class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-reverse_image"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-reverse_image"></ul>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">

            <div class="radio">
                {foreach name="statusList" item="vo"}
                <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio"
                        value="{$key}" {in name="key" value="$row.status" }checked{/in} /> {$vo}</label>
                {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group" data-favisible="status=refuse">
        <label class="control-label col-xs-12 col-sm-2">{:__('拒绝理由')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-refuse_remark" class="form-control" name="row[refuse_remark]" type="text"
                value="{$row.refuse_remark|htmlentities}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>