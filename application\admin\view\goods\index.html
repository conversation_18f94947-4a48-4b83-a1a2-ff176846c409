<div class="panel panel-default panel-intro">

    <div class="panel-heading">
        {:build_heading(null,FALSE)}
        <ul class="nav nav-tabs" data-field="status">
            <li class="{:$Think.get.status === null ? 'active' : ''}"><a href="#t-all" data-value=""
                    data-toggle="tab">{:__('All')}</a></li>
            {foreach name="statusList" item="vo"}
            <li class="{:$Think.get.status === (string)$key ? 'active' : ''}"><a href="#t-{$key}" data-value="{$key}"
                    data-toggle="tab">{$vo}</a></li>
            {/foreach}
        </ul>
    </div>

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}"><i
                                class="fa fa-refresh"></i> </a>
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                        data-operate-edit="{:$auth->check('goods/edit')}"
                        data-operate-del="{:$auth->check('goods/del')}" width="100%">
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script type="text/html" id="itemtpl">
        <% if(i == 0){ %>
            <div class="order-head">
                <div class="col-sm-3 text-center obr flex">货源信息</div>
                <div class="col-sm-2 text-center obr">装货信息</div>
                <div class="col-sm-2 text-center obr">卸货信息</div>
                <div class="col-sm-2 text-center obr">支付信息</div>
                <div class="col-sm-1 text-center obr">审核状态</div>
                <div class="col-sm-1 text-center obr">上架状态</div>
                <div class="col-sm-1 text-center">操作</div>
            </div>
        <% } %>

        <div class="col-sm-12 mt-15">
            <div class="item-top">
                <span>ID：<%=item.id%></span>
                <span style="margin-left:100px;">订单号：<%=item.orderid%></span>
                <span style="margin-left:100px;">发布时间：<%=Moment(item.createtime*1000).format("YYYY-MM-DD HH:mm:ss")%></span>
                <span style="margin-left:100px;">货主：<%=item.user ? item.user.nickname : '未知'%></span>
                <% if(item.paytime){ %>
                <span style="margin-left:100px;">支付时间：<%=Moment(item.paytime*1000).format("YYYY-MM-DD HH:mm:ss")%></span>
                <% } %>
            </div>
            <div class="item-content">
                <!-- 货源信息 col-sm-3 -->
                <div class="col-sm-3 vhc obr" style="padding: 10px;">
                    <div class="goods-info">
                        <div class="goods-type">
                            <strong><%=item.goods_type%></strong>
                            <% if(item.car_name){ %>
                            <br/><span class="label label-info"><%=item.car_name%></span>
                            <% } %>
                        </div>
                        <div class="goods-type-badge" style="margin-top: 5px;">
                            <span class="label <%=item.car_type == 'all' ? 'label-primary' : 'label-warning'%>">
                                <%=item.car_type == 'all' ? '整车' : '拼车'%>
                            </span>
                            <span class="label <%=item.time_type == 'danger' ? 'label-danger' : 'label-default'%>">
                                <%=item.time_type == 'danger' ? '紧急' : '常规'%>
                            </span>
                        </div>
                        <div class="goods-details" style="margin-top: 5px; font-size: 12px;">
                            <% if(item.weight){ %><div>重量：<%=item.weight%></div><% } %>
                            <% if(item.volume){ %><div>体积：<%=item.volume%></div><% } %>
                            <% if(item.long){ %><div>车长：<%=item.long%></div><% } %>
                            <% if(item.mobile){ %><div class="text-info"><i class="fa fa-phone"></i> 货主电话：<%=item.mobile%></div><% } %>
                            <% if(item.distance){ %><div class="text-muted">距离：<%=item.distance%>公里</div><% } %>
                        </div>
                    </div>
                </div>

                <!-- 装货信息 col-sm-2 -->
                <div class="col-sm-2 vhc obr flex">
                    <div class="address-info">
                        <div><strong><%=item.get_address || '未设置'%></strong></div>
                        <% if(item.get_username){ %>
                        <div>联系人：<%=item.get_username%></div>
                        <% } %>
                        <% if(item.get_mobile){ %>
                        <div>电话：<%=item.get_mobile%></div>
                        <% } %>
                        <div class="text-muted">
                            装货时间：<%=item.get_date%>
                            <% if(item.get_datetime){ %> <%=item.get_datetime%><% } %>
                        </div>
                    </div>
                </div>
                <div class="col-sm-2 vhc obr flex">
                     <!-- 卸货信息 col-sm-2 -->
                    <div class="address-info" >
                        <div><strong><%=item.send_address || '未设置'%></strong></div>
                        <% if(item.send_username){ %>
                        <div>联系人：<%=item.send_username%></div>
                        <% } %>
                        <% if(item.send_mobile){ %>
                        <div>电话：<%=item.send_mobile%></div>
                        <% } %>
                        <% if(item.send_date){ %>
                        <div class="text-muted">
                            卸货时间：<%=item.send_date%>
                            <% if(item.send_datetime){ %> <%=item.send_datetime%><% } %>
                        </div>
                        <% } %>
                    </div>
                </div>
                <!-- 支付信息 col-sm-1 -->
                <div class="col-sm-2 vhc obr flex">
                    <div class="payment-info text-center">
                        <div><strong>¥<%=item.amount || '0.00'%></strong></div>
                        <% if(item.paytype_text){ %>
                        <div><small class="text-muted"><%=item.paytype_text%></small></div>
                        <% } %>
                    </div>
                </div>

                <!-- 审核状态 col-sm-1 -->
                <div class="col-sm-1 vhc obr flex">
                    <span class="label <%=item.status == 'pass' ? 'label-success' : (item.status == 'refuse' ? 'label-danger' : (item.status == 'audit' ? 'label-info' : 'label-default'))%>">
                        <%=item.status == 'pass' ? '已通过' : (item.status == 'refuse' ? '已拒绝' : (item.status == 'audit' ? '审核中' : '待支付'))%>
                    </span>
                </div>

                <!-- 上架状态 col-sm-1 -->
                <div class="col-sm-1 vhc obr flex">
                    <span class="label <%=item.is_show == 'normal' ? 'label-success' : 'label-default'%>">
                        <%=item.is_show_text || (item.is_show == 'normal' ? '上架' : '下架')%>
                    </span>
                </div>

                <!-- 操作 col-sm-1 -->
                <div class="col-sm-1 vhc obr flex" style="text-align: center;">
                    <div style="height: 100px; display: flex; flex-direction: column; justify-content: center; align-items: center;">
                        <% if(item.status == 'audit'){ %>
                        <a href="javascript:;" class="audit-pass btn btn-xs btn-success" data-id="<%=item.id%>" style="margin-bottom: 3px;"><i class="fa fa-check"></i> 审核通过</a>
                        <a href="javascript:;" class="audit-refuse btn btn-xs btn-danger" data-id="<%=item.id%>" style="margin-bottom: 3px;"><i class="fa fa-times"></i> 审核拒绝</a>
                        <% } else if(item.status == 'pass'){ %>
                        <a href="javascript:;" class="toggle-show btn btn-xs <%=item.is_show == 'normal' ? 'btn-warning' : 'btn-success'%>" data-id="<%=item.id%>" data-show="<%=item.is_show%>" style="margin-bottom: 3px;"><i class="fa <%=item.is_show == 'normal' ? 'fa-eye-slash' : 'fa-eye'%>"></i> <%=item.is_show == 'normal' ? '下架' : '上架'%></a>
                        <% } else if(item.status == 'pending'){ %>
                        <span class="btn btn-xs btn-default" style="margin-bottom: 3px;"><i class="fa fa-clock-o"></i> 待支付</span>
                        <% } else if(item.status == 'refuse'){ %>
                        <span class="btn btn-xs btn-default" style="margin-bottom: 3px;"><i class="fa fa-ban"></i> 已拒绝</span>
                        <% } %>
                        <!-- 删除按钮 - 所有状态都可以删除 -->
                        <% if(Config.operate.del){ %>
                        <a href="javascript:;" class="delete-goods btn btn-xs btn-danger" data-id="<%=item.id%>"><i class="fa fa-trash"></i> 删除</a>
                        <% } %>
                    </div>
                </div>
            </div>
            <% if(item.remark){ %>
            <div class="item-remark">
                <small class="text-muted"><i class="fa fa-comment"></i> 备注：<%=item.remark%></small>
            </div>
            <% } %>
            <% if(item.refuse_remark && item.status == 'refuse'){ %>
            <div class="item-refuse">
                <small class="text-danger"><i class="fa fa-exclamation-triangle"></i> 拒绝理由：<%=item.refuse_remark%></small>
            </div>
            <% } %>
        </div>
    </script>

    <style>
        .flex {
            display: flex;
            flex-wrap: wrap;
        }

        .mt-15 {
            margin-top: 15px;
        }

        .item-goods {
            border-bottom: solid 1px #e6e6e6;
            padding: 15px;
        }

        .item-goods:last-child {
            border: none;
        }

        .ctr {
            text-align: right;
            line-height: 35px;
            font-weight: bold;
        }

        .vhc {
            padding: 15px;
            height: 100%;
            align-items: center;
            justify-content: center;
        }

        .order-head {
            background: #f7f7f7;
            padding: 0px 15px;
            width: 100%;
            font-weight: bold;
            margin-top: 5px;
        }

        .order-head div {
            padding: 15px;
        }

        .item-top {
            background: #f7f7f7;
            padding: 10px;
            width: 100%;
            border: 1px solid #e6e6e6;
        }

        .item-content {
            border: 1px solid #e6e6e6;
            border-top: none;
        }

        .obr {
            border-right: 1px solid #e6e6e6;
        }

        /*.fixed-table-toolbar{display: none;}*/
    </style>

</div>