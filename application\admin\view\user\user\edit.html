<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    {:token()}
    <input type="hidden" name="row[id]" value="{$row.id|htmlentities}">
    <div class="form-group">
        <label for="c-username" class="control-label col-xs-12 col-sm-2">{:__('Username')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-username" data-rule="required" class="form-control" disabled name="row[username]" type="text"
                value="{$row.username|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-nickname" class="control-label col-xs-12 col-sm-2">{:__('Nickname')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-nickname" data-rule="required" class="form-control" name="row[nickname]" type="text"
                value="{$row.nickname|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-password" class="control-label col-xs-12 col-sm-2">{:__('Password')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-password" data-rule="password" class="form-control" name="row[password]" type="password"
                value="" placeholder="{:__('Leave password blank if dont want to change')}"
                autocomplete="new-password" />
        </div>
    </div>
    <div class="form-group">
        <label for="c-mobile" class="control-label col-xs-12 col-sm-2">{:__('Mobile')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-mobile" data-rule="mobile" class="form-control" name="row[mobile]" type="text"
                value="{$row.mobile|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-avatar" class="control-label col-xs-12 col-sm-2">{:__('Avatar')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-avatar" data-rule="" class="form-control" size="50" name="row[avatar]" type="text"
                    value="{$row.avatar|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-avatar" class="btn btn-danger faupload"
                            data-input-id="c-avatar" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp"
                            data-multiple="false" data-preview-id="p-avatar"><i class="fa fa-upload"></i>
                            {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-avatar" class="btn btn-primary fachoose"
                            data-input-id="c-avatar" data-mimetype="image/*" data-multiple="false"><i
                                class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-avatar"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-avatar"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Gender')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[gender]', ['1'=>__('Male'), '0'=>__('Female')], $row['gender'])}
        </div>
    </div>
    <div class="form-group">
        <label for="c-free_number" class="control-label col-xs-12 col-sm-2">{:__('免费次数')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-free_number" class="form-control" name="row[free_number]" type="number"
                value="{$row.free_number|htmlentities}">
        </div>
    </div>
    <!-- <div class="form-group">
        <label for="c-successions" class="control-label col-xs-12 col-sm-2">{:__('Successions')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-successions" data-rule="required" class="form-control" name="row[successions]" type="number"
                value="{$row.successions|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-maxsuccessions" class="control-label col-xs-12 col-sm-2">{:__('Maxsuccessions')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-maxsuccessions" data-rule="required" class="form-control" name="row[maxsuccessions]"
                type="number" value="{$row.maxsuccessions|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-prevtime" class="control-label col-xs-12 col-sm-2">{:__('Prevtime')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-prevtime" data-rule="required" class="form-control datetimepicker"
                data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[prevtime]" type="text"
                value="{$row.prevtime|datetime}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-logintime" class="control-label col-xs-12 col-sm-2">{:__('Logintime')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-logintime" data-rule="required" class="form-control datetimepicker"
                data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[logintime]" type="text"
                value="{$row.logintime|datetime}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-loginip" class="control-label col-xs-12 col-sm-2">{:__('Loginip')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-loginip" data-rule="required" class="form-control" name="row[loginip]" type="text"
                value="{$row.loginip|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-loginfailure" class="control-label col-xs-12 col-sm-2">{:__('Loginfailure')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-loginfailure" data-rule="required" class="form-control" name="row[loginfailure]" type="number"
                value="{$row.loginfailure|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-joinip" class="control-label col-xs-12 col-sm-2">{:__('Joinip')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-joinip" data-rule="required" class="form-control" name="row[joinip]" type="text"
                value="{$row.joinip|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-jointime" class="control-label col-xs-12 col-sm-2">{:__('Jointime')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-jointime" data-rule="required" class="form-control datetimepicker"
                data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[jointime]" type="text"
                value="{$row.jointime|datetime}">
        </div>
    </div> -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[status]', ['normal'=>__('Normal'), 'hidden'=>__('Hidden')], $row['status'])}
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>