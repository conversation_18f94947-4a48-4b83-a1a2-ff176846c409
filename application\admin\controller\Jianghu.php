<?php

namespace app\admin\controller;

use app\common\controller\Backend;

/**
 * 卡友江湖
 *
 * @icon fa fa-circle-o
 */
class Jianghu extends Backend
{

    /**
     * Jianghu模型对象
     * @var \app\admin\model\Jianghu
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Jianghu;
        $this->view->assign("kindList", $this->model->getKindList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 查看详情
     */
    public function detail($id = null)
    {
        if (!$id) {
            $this->error(__('Invalid parameters'));
        }

        // 获取江湖动态详情
        $jianghu = $this->model->get($id);
        if (!$jianghu) {
            $this->error(__('No Results were found'));
        }

        // 获取评论列表
        $comments = \think\Db::name('jianghu_comment')
            ->where('jianghu_id', $id)
            ->order('createtime desc')
            ->select();

        // 获取点赞列表
        $clicks = \think\Db::name('jianghu_click')
            ->alias('c')
            ->join('user u', 'c.user_id = u.id', 'LEFT')
            ->where('c.jianghu_id', $id)
            ->field('c.*, u.nickname, u.avatar')
            ->order('c.createtime desc')
            ->select();

        // 获取分类信息（如果是求助类型）
        $category = null;
        if ($jianghu->kind == 'help' && $jianghu->jianghu_category_id) {
            $category = \think\Db::name('jianghu_category')
                ->where('id', $jianghu->jianghu_category_id)
                ->find();
        }

        $this->view->assign([
            'jianghu' => $jianghu,
            'comments' => $comments,
            'clicks' => $clicks,
            'category' => $category
        ]);

        return $this->view->fetch();
    }

    /**
     * 删除评论
     */
    public function delcomment()
    {
        if (!$this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }

        $id = $this->request->post('id');
        if (!$id) {
            $this->error(__('Parameter %s can not be empty', 'id'));
        }

        $comment = \think\Db::name('jianghu_comment')->where('id', $id)->find();
        if (!$comment) {
            $this->error(__('No Results were found'));
        }

        $result = \think\Db::name('jianghu_comment')->where('id', $id)->delete();
        if ($result) {
            $this->success(__('删除成功'));
        } else {
            $this->error(__('删除失败'));
        }
    }

    /**
     * 审核动态
     */
    public function audit()
    {
        if (!$this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }

        $id = $this->request->post('id');
        $status = $this->request->post('status');
        $refuse_remark = $this->request->post('refuse_remark', '');

        if (!$id || !$status) {
            $this->error(__('Parameter can not be empty'));
        }

        if (!in_array($status, ['pass', 'refuse'])) {
            $this->error(__('Invalid status'));
        }

        $jianghu = $this->model->get($id);
        if (!$jianghu) {
            $this->error(__('No Results were found'));
        }

        $data = [
            'status' => $status,
            'refuse_remark' => $status == 'refuse' ? $refuse_remark : ''
        ];

        $result = $this->model->where('id', $id)->update($data);
        if ($result !== false) {
            $statusText = $status == 'pass' ? '审核通过' : '审核拒绝';
            $this->success($statusText . '成功');
        } else {
            $this->error(__('Operation failed'));
        }
    }


}
