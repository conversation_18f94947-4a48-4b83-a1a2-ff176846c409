define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'tarpaulin_evaluate/index' + location.search,
                    add_url: 'tarpaulin_evaluate/add?tarpaulin_id=' + Config.tarpaulin_id,
                    edit_url: 'tarpaulin_evaluate/edit',
                    del_url: 'tarpaulin_evaluate/del',
                    multi_url: 'tarpaulin_evaluate/multi',
                    import_url: 'tarpaulin_evaluate/import',
                    table: 'tarpaulin_evaluate',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'weigh',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'tarpaulin_id', title: __('Tarpaulin_id'), visible: false},
                        {field: 'nickname', title: __('Nickname'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'avatar', title: __('Avatar'), operate: 'LIKE', events: Table.api.events.image, formatter: Table.api.formatter.image},
                        {field: 'content', title: __('评价内容')},
                        {field: 'images', title: __('评价图片'), operate: false, events: Table.api.events.image, formatter: Table.api.formatter.images},
                        {field: 'star', title: __('Star')},
                        {field: 'weigh', title: __('Weigh'), operate: false},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
