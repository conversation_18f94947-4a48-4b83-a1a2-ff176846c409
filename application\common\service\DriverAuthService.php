<?php

namespace app\common\service;

use app\admin\model\Driver;

/**
 * 司机认证服务类
 */
class DriverAuthService
{
    /**
     * 认证类型映射
     */
    const AUTH_TYPE_MAP = [
        'driver_card' => 'card',
        'driver_license' => 'license', 
        'driver_driving' => 'driving',
        'driver_work' => 'work',
        'driver_car' => 'car'
    ];

    /**
     * 处理认证状态变更
     * @param string $tableName 表名
     * @param int $userId 用户ID
     * @param string $status 新状态 pending|pass|refuse
     */
    public static function handleAuthStatusChange($tableName, $userId, $status)
    {
        // 获取认证类型
        $authType = self::AUTH_TYPE_MAP[$tableName] ?? null;
        if (!$authType) {
            return false;
        }

        // 根据状态映射到新的状态值
        $authStatus = 0; // 默认未上传
        switch ($status) {
            case 'pending':
                $authStatus = 1; // 待审核
                break;
            case 'pass':
                $authStatus = 2; // 已通过
                break;
            case 'refuse':
                $authStatus = 3; // 已拒绝
                break;
            default:
                $authStatus = 0; // 未上传
                break;
        }

        // 更新司机认证状态
        Driver::updateAuthStatus($userId, $authType, $authStatus);

        return true;
    }

    /**
     * 批量同步认证状态（用于数据迁移）
     */
    public static function syncAllAuthStatus()
    {
        $tables = [
            'driver_card' => 'card',
            'driver_license' => 'license',
            'driver_driving' => 'driving',
            'driver_work' => 'work',
            'driver_car' => 'car'
        ];

        foreach ($tables as $tableName => $authType) {
            // 获取所有已通过认证的记录
            $passedRecords = \think\Db::name($tableName)
                ->where('status', 'pass')
                ->field('user_id')
                ->select();

            foreach ($passedRecords as $record) {
                Driver::updateAuthStatus($record['user_id'], $authType, 2); // 已通过
            }

            // 获取所有待审核的记录
            $pendingRecords = \think\Db::name($tableName)
                ->where('status', 'pending')
                ->field('user_id')
                ->select();

            foreach ($pendingRecords as $record) {
                Driver::updateAuthStatus($record['user_id'], $authType, 1); // 待审核
            }

            // 获取所有被拒绝的记录
            $refusedRecords = \think\Db::name($tableName)
                ->where('status', 'refuse')
                ->field('user_id')
                ->select();

            foreach ($refusedRecords as $record) {
                Driver::updateAuthStatus($record['user_id'], $authType, 3); // 已拒绝
            }
        }

        return true;
    }

    /**
     * 获取用户认证进度
     * @param int $userId 用户ID
     * @return array
     */
    public static function getUserAuthProgress($userId)
    {
        $authStatus = Driver::getUserAuthStatus($userId);

        $total = 5; // 总共5项认证
        // 只有状态为2（已通过）的才算完成
        $completed = 0;
        $completed += ($authStatus['car'] == 2) ? 1 : 0;
        $completed += ($authStatus['card'] == 2) ? 1 : 0;
        $completed += ($authStatus['driving'] == 2) ? 1 : 0;
        $completed += ($authStatus['license'] == 2) ? 1 : 0;
        $completed += ($authStatus['work'] == 2) ? 1 : 0;

        $progress = round(($completed / $total) * 100, 2);

        return [
            'total' => $total,
            'completed' => $completed,
            'progress' => $progress,
            'is_all_completed' => $authStatus['all_audit'] == 1
        ];
    }

    /**
     * 检查用户是否可以发布货源（需要全部认证通过）
     * @param int $userId 用户ID
     * @return bool
     */
    public static function canPublishGoods($userId)
    {
        $authStatus = Driver::getUserAuthStatus($userId);
        return $authStatus['all_audit'] == 1;
    }

    /**
     * 获取认证状态文本
     * @param int $status 状态值
     * @return string
     */
    public static function getAuthStatusText($status)
    {
        $statusMap = [
            0 => '未上传',
            1 => '待审核',
            2 => '已通过',
            3 => '已拒绝'
        ];
        return $statusMap[$status] ?? '未知状态';
    }

    /**
     * 获取认证项目名称
     * @param string $authType 认证类型
     * @return string
     */
    public static function getAuthTypeName($authType)
    {
        $names = [
            'car' => '人车合影认证',
            'card' => '身份证认证',
            'driving' => '行驶证认证',
            'license' => '驾驶证认证',
            'work' => '从业资格证认证'
        ];

        return $names[$authType] ?? '未知认证';
    }

    /**
     * 清理孤立的认证数据（没有对应司机记录的认证数据）
     */
    public static function cleanOrphanedAuthData()
    {
        $tables = [
            'driver_card' => '身份证认证',
            'driver_license' => '驾驶证认证',
            'driver_driving' => '行驶证认证',
            'driver_work' => '从业资格证认证',
            'driver_car' => '人车合影认证'
        ];

        $cleanedCount = 0;

        foreach ($tables as $table => $name) {
            try {
                // 查找没有对应司机记录的认证数据
                $orphanedRecords = \think\Db::name($table)
                    ->alias('auth')
                    ->leftJoin('driver d', 'auth.user_id = d.user_id')
                    ->where('d.user_id', 'null')
                    ->field('auth.id, auth.user_id')
                    ->select();

                if (!empty($orphanedRecords)) {
                    $orphanedIds = array_column($orphanedRecords, 'id');
                    $deletedCount = \think\Db::name($table)->where('id', 'in', $orphanedIds)->delete();
                    $cleanedCount += $deletedCount;

                    \think\Log::info("清理{$name}孤立数据: {$deletedCount}条");
                }
            } catch (\Exception $e) {
                \think\Log::error("清理{$name}孤立数据失败: " . $e->getMessage());
            }
        }

        return $cleanedCount;
    }

    /**
     * 检查数据一致性
     * @return array 返回不一致的数据统计
     */
    public static function checkDataConsistency()
    {
        $inconsistencies = [];

        // 检查司机表中有记录但认证表中没有对应数据的情况
        $drivers = \think\Db::name('driver')->field('user_id')->select();

        foreach ($drivers as $driver) {
            $userId = $driver['user_id'];
            $authStatus = Driver::getUserAuthStatus($userId);

            $tables = [
                'driver_card' => 'card',
                'driver_license' => 'license',
                'driver_driving' => 'driving',
                'driver_work' => 'work',
                'driver_car' => 'car'
            ];

            foreach ($tables as $table => $field) {
                $authRecord = \think\Db::name($table)->where('user_id', $userId)->find();
                $driverStatus = $authStatus[$field];

                // 如果司机表显示有状态但认证表没有记录
                if ($driverStatus > 0 && !$authRecord) {
                    $inconsistencies[] = [
                        'user_id' => $userId,
                        'type' => $field,
                        'issue' => '司机表有状态但认证表无记录',
                        'driver_status' => $driverStatus
                    ];
                }

                // 如果认证表有记录但司机表状态不匹配
                if ($authRecord && $driverStatus == 0) {
                    $inconsistencies[] = [
                        'user_id' => $userId,
                        'type' => $field,
                        'issue' => '认证表有记录但司机表状态为0',
                        'auth_status' => $authRecord['status'] ?? 'unknown'
                    ];
                }
            }
        }

        return $inconsistencies;
    }
}
