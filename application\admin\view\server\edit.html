<style>
    .detail-container {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        overflow: hidden;
    }
    .section-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        font-size: 18px;
        font-weight: 600;
        display: flex;
        align-items: center;
    }
    .section-header.payment {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    }
    .section-header i {
        margin-right: 10px;
    }
    .section-content {
        padding: 20px;
    }
    .detail-item {
        display: flex;
        margin-bottom: 15px;
        align-items: center;
        min-height: 40px;
    }
    .detail-item:last-child {
        margin-bottom: 0;
    }
    .detail-label {
        width: 120px;
        font-weight: 600;
        color: #6c757d;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        height: 100%;
    }
    .detail-value {
        flex: 1;
        padding-left: 20px;
        color: #495057;
        word-break: break-all;
        display: flex;
        align-items: center;
        min-height: 40px;
    }
    .status-badge {
        display: inline-block;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
    }
    .status-normal { background: #d4edda; color: #155724; }
    .status-hidden { background: #f8d7da; color: #721c24; }
    .amount-highlight {
        font-size: 20px;
        font-weight: 700;
        color: #28a745;
    }
    .image-gallery {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        align-items: center;
    }
    .image-item {
        width: 80px;
        height: 80px;
        border-radius: 6px;
        overflow: hidden;
        border: 2px solid #e9ecef;
        cursor: pointer;
    }
    .image-item img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    .rating-display {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    .stars {
        color: #ffc107;
        font-size: 16px;
    }
    .contact-info {
        background: #e8f4fd;
        padding: 12px;
        border-radius: 6px;
        border-left: 4px solid #007bff;
        display: flex;
        align-items: center;
    }
    .detail-item.has-special-content .detail-value {
        align-items: flex-start;
        padding-top: 8px;
    }
    .mb-30 { margin-bottom: 30px; }
</style>

<div class="detail-container">
    <!-- 基础信息部分 -->
    <div class="section-header">
        <i class="fa fa-info-circle"></i>
        基础信息
    </div>
    <div class="section-content">
        <div class="detail-item">
            <div class="detail-label">标题：</div>
            <div class="detail-value">{$row.name|default='-'}</div>
        </div>

        <div class="detail-item has-special-content">
            <div class="detail-label">封面图：</div>
            <div class="detail-value">
                {if condition="$row.image"}
                    <div class="image-gallery">
                        <div class="image-item">
                            <img src="{$row.image}" alt="封面图" onclick="previewImage(this.src)">
                        </div>
                    </div>
                {else/}
                    <span class="text-muted">暂无图片</span>
                {/if}
            </div>
        </div>

        <div class="detail-item has-special-content">
            <div class="detail-label">轮播图：</div>
            <div class="detail-value">
                {if condition="$row.images"}
                    <div class="image-gallery">
                        {php}
                            $images = explode(',', $row['images']);
                            foreach($images as $image) {
                                if(!empty(trim($image))) {
                                    echo '<div class="image-item">';
                                    echo '<img src="'.trim($image).'" alt="轮播图" onclick="previewImage(this.src)">';
                                    echo '</div>';
                                }
                            }
                        {/php}
                    </div>
                {else/}
                    <span class="text-muted">暂无相册</span>
                {/if}
            </div>
        </div>

        <div class="detail-item">
            <div class="detail-label">地址：</div>
            <div class="detail-value">{$row.address|default='-'}</div>
        </div>

      
        <div class="detail-item has-special-content">
            <div class="detail-label">描述：</div>
            <div class="detail-value">{$row.content|default='-'|nl2br}</div>
        </div>

        <div class="detail-item">
            <div class="detail-label">评分：</div>
            <div class="detail-value">
                <div class="rating-display">
                    <div class="stars">
                        {for start="1" end="6"}
                            {if condition="$i <= $row.rate"}
                                <i class="fa fa-star"></i>
                            {else/}
                                <i class="fa fa-star-o"></i>
                            {/if}
                        {/for}
                    </div>
                    <span>{$row.rate|default='0'} 分 ({$row.rate_num|default='0'} 人评价)</span>
                </div>
            </div>
        </div>

        <div class="detail-item">
            <div class="detail-label">联系电话：</div>
            <div class="detail-value">
                <div class="contact-info">
                    <i class="fa fa-phone"></i> {$row.mobile|default='-'}
                </div>
            </div>
        </div>

    
        <div class="detail-item">
            <div class="detail-label">发布用户：</div>
            <div class="detail-value">{$row.nickname|default='-'}</div>
        </div>
    </div>
</div>

<div class="detail-container mb-30" style="margin-top: 20px;">
    <!-- 支付信息部分 -->
    <div class="section-header payment">
        <i class="fa fa-credit-card"></i>
        支付信息
    </div>
    <div class="section-content">
        <div class="detail-item">
            <div class="detail-label">订单号：</div>
            <div class="detail-value">
                <code>{$row.orderid|default='-'}</code>
            </div>
        </div>

        <div class="detail-item">
            <div class="detail-label">订单状态：</div>
            <div class="detail-value">
                {switch name="row.status"}
                    {case value="pending"}
                        <span class="status-badge status-normal">待支付</span>
                    {/case}
                    {case value="paid"}
                        <span class="status-badge status-hidden">已支付</span>
                    {/case}
                    {default /}
                        <span class="status-badge status-normal">{$row.status|default='-'}</span>
                {/switch}
            </div>
        </div>

        <div class="detail-item">
            <div class="detail-label">支付金额：</div>
            <div class="detail-value">
                <span class="amount-highlight">¥ {$row.amount|default='0.00'}</span>
            </div>
        </div>

        <div class="detail-item">
            <div class="detail-label">支付方式：</div>
            <div class="detail-value">
                {switch name="row.paytype"}
                    {case value="wechat"}
                        <i class="fa fa-wechat text-success"></i> 微信支付
                    {/case}
                    {case value="alipay"}
                        <i class="fa fa-alipay text-primary"></i> 支付宝
                    {/case}
                    {case value="balance"}
                        <i class="fa fa-credit-card text-warning"></i> 余额支付
                    {/case}
                    {default /}
                        {$row.paytype|default='-'}
                {/switch}
            </div>
        </div>

        <div class="detail-item">
            <div class="detail-label">支付时间：</div>
            <div class="detail-value">
                {if condition="$row.paytime"}
                    <i class="fa fa-clock-o"></i> {:date('Y-m-d H:i:s', $row.paytime)}
                {else/}
                    <span class="text-danger">未支付</span>
                {/if}
            </div>
        </div>

        <div class="detail-item">
            <div class="detail-label">交易流水号：</div>
            <div class="detail-value">
                <code>{$row.transid|default='-'}</code>
            </div>
        </div>
    </div>
</div>

<!-- 图片预览模态框 -->
<div class="modal fade" id="imageModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">图片预览</h4>
            </div>
            <div class="modal-body text-center">
                <img id="previewImg" src="" style="max-width: 100%; height: auto;">
            </div>
        </div>
    </div>
</div>

<script>
function previewImage(src) {
    $('#previewImg').attr('src', src);
    $('#imageModal').modal('show');
}
</script>

