define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'driver/index' + location.search,
                    add_url: 'driver/add',
                    edit_url: 'driver/edit',
                    del_url: 'driver/del',
                    multi_url: 'driver/multi',
                    import_url: 'driver/import',
                    table: 'driver',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                showToggle: false,
                showColumns: false,
                showExport: false,
                columns: [
                    [
                        { checkbox: true },
                        { field: 'id', title: __('Id') },
                        { field: 'user.nickname', title: __('User_id'), operate: 'LIKE' },
                        { field: 'car', title: __('Car'), searchList: { "0": "未上传", "1": "待审核", "2": "已通过", "3": "已拒绝" }, formatter: Table.api.formatter.status },
                        { field: 'card', title: __('Card'), searchList: { "0": "未上传", "1": "待审核", "2": "已通过", "3": "已拒绝" }, formatter: Table.api.formatter.status },
                        { field: 'driving', title: __('Driving'), searchList: { "0": "未上传", "1": "待审核", "2": "已通过", "3": "已拒绝" }, formatter: Table.api.formatter.status },
                        { field: 'license', title: __('License'), searchList: { "0": "未上传", "1": "待审核", "2": "已通过", "3": "已拒绝" }, formatter: Table.api.formatter.status },
                        { field: 'work', title: __('Work'), searchList: { "0": "未上传", "1": "待审核", "2": "已通过", "3": "已拒绝" }, formatter: Table.api.formatter.status },
                        { field: 'all_audit', title: __('All_audit'), searchList: { "0": "未全部认证", "1": "全部认证" }, formatter: Table.api.formatter.status },
                        { field: 'createtime', title: __('Createtime'), operate: 'RANGE', addclass: 'datetimerange', autocomplete: false, formatter: Table.api.formatter.datetime },
                        {
                            field: 'buttons',
                            width: "120px",
                            title: __('审核操作'),
                            table: table,
                            events: Table.api.events.operate,
                            buttons: [
                                {
                                    name: 'car_detail',
                                    title: __('人车合影认证'),
                                    classname: 'btn btn-xs btn-primary btn-dialog',
                                    extend: 'data-toggle="tooltip"',
                                    icon: 'fa fa-car',
                                    url: 'driver_car/edit?user_id={user_id}',
                                    callback: function (data) {
                                        Layer.alert("接收到回传数据：" + JSON.stringify(data), { title: "回传数据" });
                                    },
                                    visible: function (row) {
                                        //返回true时按钮显示,返回false隐藏
                                        return row.car != 0;
                                    }
                                },
                                {
                                    name: 'card_detail',
                                    title: __('身份证认证'),
                                    classname: 'btn btn-xs btn-success btn-dialog',
                                    extend: 'data-toggle="tooltip"',
                                    icon: 'fa fa-id-card',
                                    url: 'driver_card/edit?user_id={user_id}',
                                    callback: function (data) {
                                        Layer.alert("接收到回传数据：" + JSON.stringify(data), { title: "回传数据" });
                                    },
                                    visible: function (row) {
                                        //返回true时按钮显示,返回false隐藏
                                        return row.card != 0;
                                    }
                                },
                                {
                                    name: 'driving_detail',
                                    title: __('行驶证认证'),
                                    classname: 'btn btn-xs btn-info btn-dialog',
                                    extend: 'data-toggle="tooltip"',
                                    icon: 'fa fa-file-text',
                                    url: 'driver_driving/edit?user_id={user_id}',
                                    callback: function (data) {
                                        Layer.alert("接收到回传数据：" + JSON.stringify(data), { title: "回传数据" });
                                    },
                                    visible: function (row) {
                                        //返回true时按钮显示,返回false隐藏
                                        return row.driving != 0;
                                    }
                                },
                                {
                                    name: 'license_detail',
                                    title: __('驾驶证认证'),
                                    classname: 'btn btn-xs btn-warning btn-dialog',
                                    extend: 'data-toggle="tooltip"',
                                    icon: 'fa fa-drivers-license',
                                    url: 'driver_license/edit?user_id={user_id}',
                                    callback: function (data) {
                                        Layer.alert("接收到回传数据：" + JSON.stringify(data), { title: "回传数据" });
                                    },
                                    visible: function (row) {
                                        //返回true时按钮显示,返回false隐藏
                                        return row.license != 0;
                                    }
                                },
                                {
                                    name: 'work_detail',
                                    title: __('从业资格证认证'),
                                    classname: 'btn btn-xs btn-danger btn-dialog',
                                    extend: 'data-toggle="tooltip"',
                                    icon: 'fa fa-certificate',
                                    url: 'driver_work/edit?user_id={user_id}',
                                    callback: function (data) {
                                        Layer.alert("接收到回传数据：" + JSON.stringify(data), { title: "回传数据" });
                                    },
                                    visible: function (row) {
                                        //返回true时按钮显示,返回false隐藏
                                        return row.work != 0;
                                    }
                                },
                                {
                                    name: 'del',
                                    title: __('删除司机认证'),
                                    classname: 'btn btn-xs btn-danger btn-delone',
                                    extend: 'data-toggle="tooltip"',
                                    icon: 'fa fa-trash',
                                    url: 'driver/del',
                                    confirm: '确认要删除该司机的所有认证信息吗？此操作将同时删除相关的审核内容，且不可恢复！',
                                    success: function (data, ret) {
                                        Layer.alert("删除成功！", function (index) {
                                            $(".btn-refresh").trigger("click");
                                            Layer.close(index);
                                        });
                                    },
                                    error: function (data, ret) {
                                        Layer.alert(ret.msg);
                                        return false;
                                    }
                                }
                            ],
                            formatter: Table.api.formatter.buttons
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            // 当选择行时启用/禁用批量删除按钮
            table.on('check.bs.table uncheck.bs.table check-all.bs.table uncheck-all.bs.table', function () {
                var ids = Table.api.selectedids(table);
                $('.btn-del-multi').toggleClass('disabled', ids.length === 0);
            });

            // 批量删除按钮事件
            $(document).on('click', '.btn-del-multi', function () {
                var ids = Table.api.selectedids(table);
                if (ids.length === 0) {
                    Toastr.error(__('Please select at least one record'));
                    return false;
                }
                Layer.confirm('确认要删除选中的司机认证信息吗？此操作将同时删除相关的审核内容，且不可恢复！', function (index) {
                    $.ajax({
                        url: 'driver/multi',
                        type: 'POST',
                        data: {ids: ids.join(','), action: 'del'},
                        dataType: 'json',
                        success: function (ret) {
                            if (ret.code === 1) {
                                Layer.alert(ret.msg + ",删除成功！", function (index) {
                                    $(".btn-refresh").trigger("click");
                                    Layer.close(index);
                                });
                            } else {
                                Layer.alert(ret.msg);
                            }
                            Layer.close(index);
                        },
                        error: function () {
                            Layer.alert('删除失败，请重试');
                            Layer.close(index);
                        }
                    });
                });
            });
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
