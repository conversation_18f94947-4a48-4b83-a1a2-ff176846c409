<?php

namespace app\admin\model;

use think\Model;


class Goods extends Model
{

    

    

    // 表名
    protected $name = 'goods';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'car_type_text',
        'time_type_text',
        'get_datetime_text',
        'send_datetime_text',
        'status_text',
        'paytime_text'
    ];
    

    
    public function getCarTypeList()
    {
        return ['all' => __('All'), 'pin' => __('Pin')];
    }

    public function getTimeTypeList()
    {
        return ['normal' => __('Normal'), 'danger' => __('Danger')];
    }

    public function getStatusList()
    {
        return ['pending' => __('Status pending'), 'audit' => __('Status audit'), 'pass' => __('Status pass'), 'refuse' => __('Status refuse')];
    }


    public function getCarTypeTextAttr($value, $data)
    {
        $value = $value ?: ($data['car_type'] ?? '');
        $list = $this->getCarTypeList();
        return $list[$value] ?? '';
    }


    public function getTimeTypeTextAttr($value, $data)
    {
        $value = $value ?: ($data['time_type'] ?? '');
        $list = $this->getTimeTypeList();
        return $list[$value] ?? '';
    }


    public function getGetDatetimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['get_datetime'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getSendDatetimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['send_datetime'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ?: ($data['status'] ?? '');
        $list = $this->getStatusList();
        return $list[$value] ?? '';
    }


    public function getPaytimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['paytime'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setGetDatetimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setSendDatetimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setPaytimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


    public function user()
    {
        return $this->belongsTo('User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    public function getAddress()
    {
        return $this->belongsTo('Address', 'get_address_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    public function sendAddress()
    {
        return $this->belongsTo('Address', 'send_address_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    public function goodsCar()
    {
        return $this->belongsTo('GoodsCar', 'goods_car_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
