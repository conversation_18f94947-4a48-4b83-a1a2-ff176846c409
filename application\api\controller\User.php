<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\library\Ems;
use app\common\library\Sms;
use fast\Random;
use think\Config;
use think\Validate;
use think\Db;

/**
 * 会员接口
 */
class User extends Api
{
    protected $noNeedLogin = ['login', 'mobilelogin', 'register', 'resetpwd', 'changeemail', 'changemobile', 'third', 'wechatlogin'];
    protected $noNeedRight = '*';

    public function _initialize()
    {
        parent::_initialize();

        if (!Config::get('fastadmin.usercenter')) {
            $this->error(__('User center already closed'));
        }
    }




    /**
     * 绑定微信
     *
     * @return void
     */
    public function bindWechat()
    {
        $openid = $this->request->post('openid');
        $puser = \app\common\model\User::getByOpenid($openid);
        if ($puser) {
            $puser->openid = '';
            $puser->save();
        }
        $user  = $this->auth->getUser();
        $user->openid = $openid;
        $user->save();
        $this->success('绑定成功');
    }

    /**
     * 解绑微信
     *
     * @return void
     */
    public function unbindWechat()
    {
        $user = $this->auth->getUser();
        $user->openid = '';
        $user->save();
        $this->success('解绑成功');
    }


    /**
     * 微信登录
     * @ApiMethod (POST)
     * @ApiParams (name="openid", type="string", required=true, description="openid")
     */
    public function wechatlogin()
    {
        $openid = $this->request->post('openid');
        $user = \app\common\model\User::getByOpenid($openid);
        if (!$user) {
            $ret = $this->auth->register(Random::alnum(), Random::alnum(), '', '', ['openid' => $openid]);
        } else {
            $ret = $this->auth->direct($user->id);
        }
        if (!$ret) {
            $this->error($this->auth->getError());
        }
        $is_bind = $this->auth->mobile ? 1 : 0;
        $this->success(__('Login successful'), ['userinfo' => $this->auth->getUserinfo(), 'is_bind' => $is_bind]);
    }

    /**
     * 绑定手机号
     */
    public function bindMobile()
    {
        $mobile = $this->request->post('mobile');
        $captcha = $this->request->post('captcha');
        if ($mobile && !Validate::regex($mobile, "^1\d{10}$")) {
            $this->error(__('Mobile is incorrect'));
        }
        if ($captcha != 8888) {
            $ret = Sms::check($mobile, $captcha, 'mobilelogin');
            if (!$ret) {
                $this->error(__('Captcha is incorrect'));
            }
        }
        Sms::flush($mobile, 'mobilelogin');
        $user = \app\common\model\User::getByMobile($mobile);
        if (!$user) {
            $user = $this->auth->getUser();
            $user->mobile = $mobile;
            $user->save();
        } else {
            $this->error('该手机号已注册');
        }
        $this->success(__('Bind mobile successful'), ['userinfo' => $this->auth->getUserinfo()]);
    }


    /**
     * 会员中心
     */
    public function index()
    {
        $userInfo = $this->auth->getUser();
        if ($userInfo['endtime'] <= time()) {
            $userInfo->is_vip = 0;
            $userInfo->endtime = 0;
            $userInfo->save();
        }
        if ($userInfo['is_vip'] == 1) {
            $userinfo['endtime'] = datetime($userInfo['endtime']);
        }
        $this->success('', ['userinfo' => $userInfo]);
    }

    /**
     * 会员登录
     *
     * @ApiMethod (POST)
     * @ApiParams (name="account", type="string", required=true, description="账号")
     * @ApiParams (name="password", type="string", required=true, description="密码")
     */
    public function login()
    {
        $account = $this->request->post('account');
        $password = $this->request->post('password');
        if (!$account || !$password) {
            $this->error(__('Invalid parameters'));
        }
        $ret = $this->auth->login($account, $password);
        if ($ret) {
            $data = ['userinfo' => $this->auth->getUserinfo()];
            $this->success(__('Logged in successful'), $data);
        } else {
            $this->error($this->auth->getError());
        }
    }

    /**
     * 手机验证码登录
     *
     * @ApiMethod (POST)
     * @ApiParams (name="mobile", type="string", required=true, description="手机号")
     * @ApiParams (name="captcha", type="string", required=true, description="验证码")
     */
    public function mobilelogin()
    {
        $mobile = $this->request->post('mobile');
        $captcha = $this->request->post('captcha');
        if (!$mobile || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        if (!Validate::regex($mobile, "^1\d{10}$")) {
            $this->error(__('Mobile is incorrect'));
        }
        if ($captcha != '8888') {
            if (!Sms::check($mobile, $captcha, 'mobilelogin')) {
                $this->error(__('Captcha is incorrect'));
            }
        }
        $user = \app\common\model\User::getByMobile($mobile);
        if ($user) {
            if ($user->status != 'normal') {
                $this->error(__('Account is locked'));
            }
            //如果已经有账号则直接登录
            $ret = $this->auth->direct($user->id);
        } else {
            $ret = $this->auth->register($mobile, Random::alnum(), '', $mobile, []);
        }
        if ($ret) {
            Sms::flush($mobile, 'mobilelogin');
            $data = ['userinfo' => $this->auth->getUserinfo()];
            $this->success(__('Logged in successful'), $data);
        } else {
            $this->error($this->auth->getError());
        }
    }

    /**
     * 注册会员
     *
     * @ApiMethod (POST)
     * @ApiParams (name="username", type="string", required=true, description="用户名")
     * @ApiParams (name="password", type="string", required=true, description="密码")
     * @ApiParams (name="email", type="string", required=true, description="邮箱")
     * @ApiParams (name="mobile", type="string", required=true, description="手机号")
     * @ApiParams (name="code", type="string", required=true, description="验证码")
     */
    public function register()
    {
        $username = $this->request->post('username');
        $password = $this->request->post('password');
        $email = $this->request->post('email');
        $mobile = $this->request->post('mobile');
        $code = $this->request->post('code');
        if (!$username || !$password) {
            $this->error(__('Invalid parameters'));
        }
        if ($email && !Validate::is($email, "email")) {
            $this->error(__('Email is incorrect'));
        }
        if ($mobile && !Validate::regex($mobile, "^1\d{10}$")) {
            $this->error(__('Mobile is incorrect'));
        }
        $ret = Sms::check($mobile, $code, 'register');
        if (!$ret) {
            $this->error(__('Captcha is incorrect'));
        }
        $ret = $this->auth->register($username, $password, $email, $mobile, []);
        if ($ret) {
            $data = ['userinfo' => $this->auth->getUserinfo()];
            $this->success(__('Sign up successful'), $data);
        } else {
            $this->error($this->auth->getError());
        }
    }

    /**
     * 退出登录
     * @ApiMethod (POST)
     */
    public function logout()
    {
        if (!$this->request->isPost()) {
            $this->error(__('Invalid parameters'));
        }
        $this->auth->logout();
        $this->success(__('Logout successful'));
    }

    /**
     * 修改会员个人信息
     *
     * @ApiMethod (POST)
     * @ApiParams (name="avatar", type="string", required=true, description="头像地址")
     * @ApiParams (name="username", type="string", required=true, description="用户名")
     * @ApiParams (name="nickname", type="string", required=true, description="昵称")
     * @ApiParams (name="bio", type="string", required=true, description="个人简介")
     */
    public function profile()
    {
        $user = $this->auth->getUser();
        $nickname = $this->request->post('nickname');
        $avatar = $this->request->post('avatar', '', 'trim,strip_tags,htmlspecialchars');
        if ($nickname) {
            $exists = \app\common\model\User::where('nickname', $nickname)->where('id', '<>', $this->auth->id)->find();
            if ($exists) {
                $this->error(__('Nickname already exists'));
            }
            $user->nickname = $nickname;
        }
        if ($avatar) {
            $user->avatar = $avatar;
        }
        $user->save();
        $this->success('修改成功');
    }

    /**
     * 修改邮箱
     *
     * @ApiMethod (POST)
     * @ApiParams (name="email", type="string", required=true, description="邮箱")
     * @ApiParams (name="captcha", type="string", required=true, description="验证码")
     */
    public function changeemail()
    {
        $user = $this->auth->getUser();
        $email = $this->request->post('email');
        $captcha = $this->request->post('captcha');
        if (!$email || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        if (!Validate::is($email, "email")) {
            $this->error(__('Email is incorrect'));
        }
        if (\app\common\model\User::where('email', $email)->where('id', '<>', $user->id)->find()) {
            $this->error(__('Email already exists'));
        }
        $result = Ems::check($email, $captcha, 'changeemail');
        if (!$result) {
            $this->error(__('Captcha is incorrect'));
        }
        $verification = $user->verification;
        $verification->email = 1;
        $user->verification = $verification;
        $user->email = $email;
        $user->save();

        Ems::flush($email, 'changeemail');
        $this->success();
    }

    /**
     * 修改手机号
     *
     * @ApiMethod (POST)
     * @ApiParams (name="mobile", type="string", required=true, description="手机号")
     * @ApiParams (name="captcha", type="string", required=true, description="验证码")
     */
    public function changemobile()
    {
        $user = $this->auth->getUser();
        $mobile = $this->request->post('mobile');
        $captcha = $this->request->post('captcha');
        if (!$mobile || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        if (!Validate::regex($mobile, "^1\d{10}$")) {
            $this->error(__('Mobile is incorrect'));
        }
        if (\app\common\model\User::where('mobile', $mobile)->where('id', '<>', $user->id)->find()) {
            $this->error(__('Mobile already exists'));
        }
        if ($captcha != '8888') {
            $result = Sms::check($mobile, $captcha, 'changemobile');
            if (!$result) {
                $this->error(__('Captcha is incorrect'));
            }
        }
        $verification = $user->verification;
        $verification->mobile = 1;
        $user->verification = $verification;
        $user->mobile = $mobile;
        $user->save();

        Sms::flush($mobile, 'changemobile');
        $this->success();
    }

    /**
     * 第三方登录
     *
     * @ApiMethod (POST)
     * @ApiParams (name="platform", type="string", required=true, description="平台名称")
     * @ApiParams (name="code", type="string", required=true, description="Code码")
     */
    // public function third()
    // {
    //     $url = url('user/index');
    //     $platform = $this->request->post("platform");
    //     $code = $this->request->post("code");
    //     $config = get_addon_config('third');
    //     if (!$config || !isset($config[$platform])) {
    //         $this->error(__('Invalid parameters'));
    //     }
    //     $app = new \addons\third\library\Application($config);
    //     //通过code换access_token和绑定会员
    //     $result = $app->{$platform}->getUserInfo(['code' => $code]);
    //     if ($result) {
    //         $loginret = \addons\third\library\Service::connect($platform, $result);
    //         if ($loginret) {
    //             $data = [
    //                 'userinfo'  => $this->auth->getUserinfo(),
    //                 'thirdinfo' => $result
    //             ];
    //             $this->success(__('Logged in successful'), $data);
    //         }
    //     }
    //     $this->error(__('Operation failed'), $url);
    // }

    /**
     * 重置密码
     *
     * @ApiMethod (POST)
     * @ApiParams (name="mobile", type="string", required=true, description="手机号")
     * @ApiParams (name="newpassword", type="string", required=true, description="新密码")
     * @ApiParams (name="captcha", type="string", required=true, description="验证码")
     */
    public function resetpwd()
    {
        $type = $this->request->post("type", "mobile");
        $mobile = $this->request->post("mobile");
        $email = $this->request->post("email");
        $newpassword = $this->request->post("newpassword");
        $captcha = $this->request->post("captcha");
        if (!$newpassword || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        //验证Token
        if (!Validate::make()->check(['newpassword' => $newpassword], ['newpassword' => 'require|regex:\S{6,30}'])) {
            $this->error(__('Password must be 6 to 30 characters'));
        }
        if ($type == 'mobile') {
            if (!Validate::regex($mobile, "^1\d{10}$")) {
                $this->error(__('Mobile is incorrect'));
            }
            $user = \app\common\model\User::getByMobile($mobile);
            if (!$user) {
                $this->error(__('User not found'));
            }
            $ret = Sms::check($mobile, $captcha, 'resetpwd');
            if (!$ret) {
                $this->error(__('Captcha is incorrect'));
            }
            Sms::flush($mobile, 'resetpwd');
        } else {
            if (!Validate::is($email, "email")) {
                $this->error(__('Email is incorrect'));
            }
            $user = \app\common\model\User::getByEmail($email);
            if (!$user) {
                $this->error(__('User not found'));
            }
            $ret = Ems::check($email, $captcha, 'resetpwd');
            if (!$ret) {
                $this->error(__('Captcha is incorrect'));
            }
            Ems::flush($email, 'resetpwd');
        }
        //模拟一次登录
        $this->auth->direct($user->id);
        $ret = $this->auth->changepwd($newpassword, '', true);
        if ($ret) {
            $this->success(__('Reset password successful'));
        } else {
            $this->error($this->auth->getError());
        }
    }

    /**
     * 添加意见反馈
     *
     * @ApiMethod (POST)
     * @ApiParams (name="content", type="string", required=true, description="反馈内容")
     */
    public function feedback()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $content = $this->request->post('content');

        // 验证必填参数
        if (!$content) {
            $this->error(__('Invalid parameters'));
        }

        // 验证内容长度
        if (mb_strlen($content) < 5) {
            $this->error('反馈内容不能少于5个字符');
        }

        if (mb_strlen($content) > 500) {
            $this->error('反馈内容不能超过500个字符');
        }

        // 检查一分钟内是否已提交过反馈（防重复提交）
        $oneMinuteAgo = time() - 60;
        $recentFeedback = Db::name('feedback')
            ->where('user_id', $user->id)
            ->where('createtime', '>', $oneMinuteAgo)
            ->find();

        if ($recentFeedback) {
            $this->error('您刚刚已提交过反馈，请稍后再试');
        }

        // 插入反馈数据
        $data = [
            'user_id' => $user->id,
            'content' => $content,
            'createtime' => time()
        ];

        try {
            $id = Db::name('feedback')->insertGetId($data);
        } catch (\Exception $e) {
            $this->error('提交失败，请重试');
        }
        $this->success('反馈提交成功，感谢您的建议', ['id' => $id]);
    }

    /**
     * 检查用户操作资格
     *
     * 逻辑说明：
     * 1. 如果用户是VIP，直接返回可查看
     * 2. 如果不是VIP，优先检查是否有付费记录，有则可查看
     * 3. 如果没有付费记录，检查是否有免费次数
     * 4. 有免费次数则扣除并创建免费记录，可查看
     * 5. 没有免费次数则需要付费或开通VIP
     *
     * @ApiMethod (GET)
     * @ApiParams (name="company_category_id", type="integer", required=true, description="企业分类ID")
     * @ApiParams (name="link_id", type="integer", required=true, description="关联ID")
     */
    public function checkOperationPermission()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error(__('Please login first'));
        }

        $hasPermission = false;
        $reason = '';
        $currentUser = null;
        $company_category_id = $this->request->get('company_category_id');
        $link_id = $this->request->get('link_id');

        // 验证必填参数
        if (!$company_category_id || !$link_id) {
            $this->error(__('Invalid parameters'));
        }

        // 获取用户最新信息
        $currentUser = Db::name('user')->where('id', $user->id)->find();

        // 1. 检查用户是否是VIP
        if ($user->is_vip == 1) {
            $hasPermission = true;
            $reason = 'VIP用户';
        } else {
            // 2. 检查用户是否有对应的付费记录
            $paidOrder = Db::name('order')
                ->where('user_id', $user->id)
                ->where('company_category_id', $company_category_id)
                ->where('link_id', $link_id)
                ->where('status', 'paid')
                ->find();

            if ($paidOrder) {
                $hasPermission = true;
                $reason = '已付费用户';
            } else {
                // 3. 检查用户是否有免费次数
                if ($currentUser && $currentUser['free_number'] > 0) {
                    // 有免费次数，扣除并创建免费记录
                    Db::startTrans();
                    try {
                        // 扣除免费次数
                        Db::name('user')
                            ->where('id', $user->id)
                            ->setDec('free_number', 1);

                        // 插入免费付费记录
                        Db::name('order')->insert([
                            'user_id' => $user->id,
                            'company_category_id' => $company_category_id,
                            'link_id' => $link_id,
                            'status' => 'paid',
                            'is_free' => 1, // 标记为免费记录
                            'amount' => 0,
                            'createtime' => time(),
                            'paytime' => time()
                        ]);

                        Db::commit();
                        $hasPermission = true;
                        $reason = '使用免费次数';
                    } catch (\Exception $e) {
                        Db::rollback();
                        $this->error('系统错误，请重试');
                    }
                } else {
                    // 没有免费次数
                    $hasPermission = false;
                    $reason = '需要付费或开通VIP';
                }
            }
        }

        $this->success('检查完成', [
            'has_permission' => $hasPermission,
            'reason' => $reason,
            'remaining_free_number' => $hasPermission && $reason == '使用免费次数' ?
                ($currentUser['free_number'] - 1) :
                ($currentUser['free_number'] ?? 0)
        ]);
    }
}
